# PHP Upload Configuration for VLR System
# Increase upload limits to support large files

# Maximum file upload size (100MB)
php_value upload_max_filesize 100M

# Maximum POST data size (120MB to accommodate multiple files)
php_value post_max_size 120M

# Maximum number of files that can be uploaded simultaneously
php_value max_file_uploads 20

# Maximum execution time for uploads (5 minutes)
php_value max_execution_time 300

# Maximum input time for uploads (5 minutes)
php_value max_input_time 300

# Memory limit for processing uploads (256MB)
php_value memory_limit 256M

# Enable file uploads
php_flag file_uploads On

# Temporary directory for uploads (optional)
# php_value upload_tmp_dir /tmp

# Security: Disable dangerous functions
php_flag allow_url_fopen Off
php_flag allow_url_include Off

# Error reporting (disable in production)
# php_flag display_errors Off
# php_flag log_errors On

# ===================================
# URL REWRITING FOR CLEAN ROUTES
# ===================================

RewriteEngine On

# Handle static assets (don't rewrite)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/(public|css|js|images|uploads|assets)/ [NC]
RewriteRule . - [L]

# Handle clean URLs - route everything through index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# ===================================
# SECURITY HEADERS
# ===================================

<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# ===================================
# PERFORMANCE OPTIMIZATION
# ===================================

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
