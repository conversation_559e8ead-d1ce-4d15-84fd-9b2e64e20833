{"_comment1": "navbar.php", "client_logo": "क्लाइंट लोगो", "search": "खोजें", "search_language": "भाषा खोजें", "change_language": "भाषा बदलें", "profile": "प्रोफ़ाइल", "logout": "लॉगआउट", "_comment2": "dashboard.php", "dashboard_welcome": "डैशबोर्ड में आपका स्वागत है", "dashboard_description": "यह आपका ई-लर्निंग प्रशासनिक पैनल है।", "_comment3": "Footer.php", "company_name": "दीपलक्ष्मी कम्युनिकेशन्स", "all_rights_reserved": "सर्वाधिकार सुरक्षित।", "_comment4": "Sidebar.php", "dashboard": "डैशबोर्ड", "manage_portal": "पोर्टल प्रबंधन", "my_courses": "मेरे पाठ्यक्रम", "search_courses": "पाठ्यक्रम खोजें", "_comment5": "Manage_portal.php", "user_details": "उपयोगकर्ता विवरण", "course_details": "पाठ्यक्रम विवरण", "social": "सोशल", "settings": "सेटिंग्स", "user_management": "उपयोगकर्ता प्रबंधन", "create_edit_remove_user": "उपयोगकर्ता बनाएं, संपादित करें, हटाएं", "user_settings": "उपयोगकर्ता सेटिंग्स", "manage_user_roles_permissions": "उपयोगकर्ता भूमिकाएँ और अनुमतियाँ प्रबंधित करें", "course_creation": "पाठ्यक्रम निर्माण", "create_courses": "ई-लर्निंग, कक्षा और मूल्यांकन पाठ्यक्रम बनाएं", "course_module_creation": "पाठ्यक्रम मॉड्यूल निर्माण", "organize_courses": "पाठ्यक्रमों को अनुभागों में व्यवस्थित करें", "course_categories": "पाठ्यक्रम श्रेणियाँ", "category": "श्रेणी", "manage_categories": "पाठ्यक्रम श्रेणियाँ जोड़ें और प्रबंधित करें", "sub_category": "उप-श्रेणी", "define_sub_categories": "पाठ्यक्रम उप-श्रेणियाँ परिभाषित करें", "course_content": "पाठ्यक्रम सामग्री", "vlr": "VLR - वर्चुअल लर्निंग रिपॉजिटरी", "manage_vlr": "SCORM, मूल्यांकन, वीडियो और अधिक प्रबंधित करें", "social_coming_soon": "सोशल सेटिंग्स अनुभाग जल्द ही आ रहा है...", "settings_coming_soon": "सेटिंग्स अनुभाग जल्द ही आ रहा है...", "_comment6": "user_management.php", "user_management_title": "उपयोगकर्ता प्रबंधन", "filters_profile_id": "प्रोफ़ाइल आईडी", "filters_full_name": "पूरा नाम", "filters_email": "ईमेल", "filters_contact_number": "संपर्क नंबर", "filters_user_status": "उपयोगकर्ता स्थिति", "filters_locked_status": "लॉक स्थिति", "filters_search_placeholder": "प्रश्न खोजें...", "buttons_add_user": "+ उपयोगकर्ता जोड़ें", "buttons_add_user_tooltip": "सिस्टम में नया उपयोगकर्ता जोड़ें", "buttons_import_user": "उपयोगकर्ता आयात करें", "user_grid_profile_id": "प्रोफ़ाइल आईडी", "user_grid_full_name": "पूरा नाम", "user_grid_email": "ईमेल", "user_grid_contact_number": "संपर्क नंबर", "user_grid_user_status": "उपयोगकर्ता स्थिति", "user_grid_locked_status": "लॉक स्थिति", "user_grid_action": "क्रिया", "user_grid_active": "सक्रिय", "user_grid_inactive": "निष्क्रिय", "user_grid_locked": "लॉक किया गया", "user_grid_unlocked": "अनलॉक किया गया", "user_grid_edit_user": "उपयोगकर्ता संपादित करें", "user_grid_lock_user": "उपयोगकर्ता को लॉक करें", "user_grid_unlock_user": "उपयोगकर्ता को अनलॉक करें", "user_grid_delete_user": "उपयोगकर्ता हटाएं", "user_grid_edit_disabled": "सुपर एडमिन को संपादित नहीं किया जा सकता", "user_grid_lock_disabled": "सुपर एडमिन को लॉक नहीं किया जा सकता", "user_grid_unlock_disabled": "सुपर एडमिन को अनलॉक नहीं किया जा सकता", "user_grid_delete_disabled": "सुपर एडमिन को हटाया नहीं जा सकता", "user_grid_no_users_found": "कोई उपयोगकर्ता नहीं मिला।", "user_grid_lock_confirm": "क्या आप वाकई इस उपयोगकर्ता को लॉक करना चाहते हैं?", "user_grid_unlock_confirm": "क्या आप वाकई इस उपयोगकर्ता को अनलॉक करना चाहते हैं?", "user_grid_delete_confirm": "क्या आप वाकई इस उपयोगकर्ता को हटाना चाहते हैं? यह क्रिया पुनः संभव है।", "pagination_prev": "पिछला", "pagination_next": "अगला", "_comment7": "add_user.php", "add_user_title": "उपयोगकर्ता जोड़ें", "edit_user_title": "उपयोगकर्ता संपादित करें", "update": "अपडेट करें", "basic_details": "मूल विवरण", "additional_details": "अतिरिक्त विवरण", "extra_details": "अतिरिक्त जानकारी", "profile_id": "प्रोफ़ाइल आईडी (स्वचालित रूप से उत्पन्न)", "full_name": "पूरा नाम", "email": "ईमेल", "contact_number": "संपर्क नंबर", "gender": "लिंग", "select_gender": "लिंग चुनें", "male": "पुरुष", "female": "महिला", "other": "अन्य", "dob": "जन्म तिथि", "user_role": "उपयोगकर्ता भूमिका", "select_user_role": "उपयोगकर्ता भूमिका चुनें", "admin": "प्र<PERSON>ासक", "end_user": "अंतिम उपयोगकर्ता", "instructor": "प्रशिक्षक", "corporate_manager": "कॉर्पोरेट प्रबंधक", "hr_manager": "एचआर प्रबंधक", "team_lead": "टीम लीड", "content_creator": "सामग्री निर्माता", "profile_expiry_date": "प्रोफ़ाइल समाप्ति तिथि", "user_status": "उपयोगकर्ता स्थिति", "active": "सक्रिय", "inactive": "निष्क्रिय", "locked_status": "लॉक स्थिति", "locked": "लॉक किया गया", "unlocked": "अनलॉक किया गया", "leaderboard": "लीडरबोर्ड पर दिखें", "yes": "हाँ", "no": "नहीं", "profile_picture": "प्रोफ़ाइल चित्र", "country": "देश", "select_country": "देश चुनें", "state": "राज<PERSON>य", "select_state": "राज्य चुनें", "city": "<PERSON><PERSON><PERSON>", "select_city": "शहर चुनें", "timezone": "समय क्षेत्र", "select_timezone": "समय क्षेत्र चुनें", "language": "भाषा", "reports_to": "रिपोर्ट करता है", "joining_date": "जॉइनिंग तिथि", "retirement_date": "सेवानिवृत्ति तिथि", "customised_1": "कस्टमाइज़्ड 1", "customised_2": "कस्टमाइज़्ड 2", "customised_3": "कस्टमाइज़्ड 3", "customised_4": "कस्टमाइज़्ड 4", "customised_5": "कस्टमाइज़्ड 5", "customised_6": "कस्टमाइज़्ड 6", "customised_7": "कस्टमाइज़्ड 7", "customised_8": "कस्टमाइज़्ड 8", "customised_9": "कस्टमाइज़्ड 9", "customised_10": "कस्टमाइज़्ड 10", "submit": "सबमिट करें", "cancel": "रद्<PERSON> करें", "_comment8": "vlr.php", "vlr_title": "वर्चुअल लर्निंग रिपॉजिटरी (VLR)", "non_scorm": "नॉन-SCORM", "assessment": "मूल्यांकन", "audio": "ऑडियो", "video": "वीडियो", "document": "दस्तावेज़", "image": "छवि", "external_content": "बाहरी सामग्री", "survey": "सर्वेक्षण", "feedback": "प्रतिक्रिया", "interactive_ai_content": "इंटरएक्टिव और एआई-संचालित सामग्री", "scorm": "SCORM", "add_scorm": "SCORM जोड़ें", "add_scorm_package": "SCORM पैकेज जोड़ें", "close": "ब<PERSON><PERSON> करें", "upload_scorm_zip": "SCORM ज़िप फ़ाइल अपलोड करें", "version": "संस्करण", "language_support": "भाषा समर्थन", "scorm_category": "SCORM श्रेणी", "select_scorm_type": "SCORM प्रकार चुनें", "scorm_1_2": "SCORM 1.2", "scorm_2004": "SCORM 2004", "tincan_api_xapi": "टिन कैन एपीआई (xAPI)", "cmi5": "CMI5", "time_limit": "समय सीमा", "assessment_included": "मूल्यांकन शामिल", "scorm-1.2_content": "SCORM 1.2 सामग्री", "scorm-2004_content": "SCORM 2004 सामग्री", "tin-can-api_content": "टिन कैन एपीआई (xAPI) सामग्री", "cmi5_content": "CMI5 सामग्री", "edit": "संपादित करें", "delete": "हटाएं", "preview": "पूर्वावलोकन", "delete_confirmation_scrom": "क्या आप वाकई इस SCORM पैकेज को हटाना चाहते हैं?", "no_scorm_found": "इस श्रेणी के लिए कोई SCORM पैकेज नहीं मिला।", "add": "जोड़ें", "documents": "दस्तावेज़", "word_excel_ppt": "वर्ड/एक्सेल/PPT फ़ाइलें", "word_excel_ppt_desc": "वर्ड, एक्सेल और पावरपॉइंट फ़ाइलें अपलोड और प्रबंधित करें।", "ebook_manual": "ई-बुक और मैनुअल", "ebook_manual_desc": "ई-बुक और मैनुअल अपलोड और प्रबंधित करें।", "research_case_studies": "शोध पत्र और केस स्टडीज़", "research_case_studies_desc": "शोध पत्र और केस स्टडीज़ अपलोड और प्रबंधित करें।", "add_external_content": "बाहरी सामग्री जोड़ें", "title": "शीर्षक", "version_number": "संस्करण संख्या", "mobile_tablet_support": "मोबाइल और टैबलेट समर्थन", "english": "अंग्रेज़ी", "hindi": "हिंदी", "marathi": "मराठी", "spanish": "स्पेनिश", "french": "फ्रेंच", "german": "जर्मन", "chinese": "चीनी", "minutes": "मिनट", "content_type": "सामग्री प्रकार", "youtube_vimeo": "यूट्यूब और वीमियो सामग्री", "linkedin_udemy": "लिंक्डइन लर्निंग, उडेमी, कौरसेरा", "podcasts_audio": "पॉडकास्ट और ऑडियो पाठ", "description": "विवरण", "tags_keywords": "टैग्स / कीवर्ड्स", "add_tag_placeholder": "एक टैग जोड़ें और Enter दबाएं", "video_url": "वीडियो URL", "thumbnail_preview": "थंबनेल पूर्वावलोकन", "course_url": "पाठ्यक्रम URL", "platform_name": "प्लेटफ़ॉर्म नाम", "select": "चयन करें", "article_url": "URL", "author_publisher": "लेखक/प्रकाशक", "audio_source": "ऑडियो स्रोत", "upload_file": "फ़ाइल अपलोड करें", "audio_url": "ऑडियो URL", "upload_audio": "ऑडियो अपलोड करें", "speaker_host": "स्पीकर / होस्ट", "youtube_vimeo_ul": "यूट्यूब और वीमियो एकीकरण", "linkedin_udemy_coursera_ul": "लिंक्डइन लर्निंग, उडेमी, कौरसेरा", "web_links_blogs_ul": "वेब लिंक और ब्लॉग", "podcasts_audio_lessons_ul": "पॉडकास्ट और ऑडियो पाठ", "linkedin_udemy_coursera": "लिंक्डइन लर्निंग, उडेमी, कौरसेरा सामग्री", "web_links_blogs": "वेब लिंक और ब्लॉग सामग्री", "podcasts_audio_lessons": "पॉडकास्ट और ऑडियो पाठ सामग्री", "confirm_delete": "क्या आप वाकई इस बाहरी सामग्री को हटाना चाहते हैं?", "no_external_content": "इस श्रेणी के लिए कोई बाहरी सामग्री नहीं मिली।", "adaptive_learning": "एडेप्टिव लर्निंग सामग्री", "chatbots_virtual_assistants": "चैटबॉट्स और वर्चुअल असिस्टेंट", "ar_vr": "ऑगमेंटेड रियलिटी (AR) / वर्चुअल रियलिटी (VR)", "adaptive_learning_desc": "एडेप्टिव लर्निंग सामग्री प्रबंधित और अनुकूलित करें।", "chatbots_virtual_assistants_desc": "एआई-संचालित चैटबॉट्स और वर्चुअल असिस्टेंट प्रबंधित करें।", "ar_vr_desc": "AR और VR आधारित इंटरएक्टिव लर्निंग अनुभवों का प्रबंधन करें।", "add_document": "दस्तावेज़ जोड़ें", "document.modal.add": "दस्तावेज़ जोड़ें", "document.modal.edit": "दस्तावेज़ संपादित करें", "select_category": "श्रेणी चुनें", "category.word_excel_ppt": "वर्ड/एक्सेल/पीपीटी फ़ाइलें", "category.ebook_manual": "ई-बुक और मैनुअल", "category.research_paper": "अनुसंधान पत्र और केस स्टडी", "upload_file.word_excel_ppt": "फ़ाइल अपलोड करें (.docx, .xlsx, .pptx, .pdf)", "upload_file.ebook_manual": "फ़ाइल अपलोड करें (.pdf, .epub, .mobi)", "upload_file.research": "फ़ाइल अपलोड करें (.pdf, .docx)", "authors": "लेखक", "publication_date": "प्रकाशन तिथि", "reference_links": "संदर्भ लिंक", "select_language": "भाषा चुनें", "no_languages_available": "कोई भाषा उपलब्ध नहीं है", "mobile_support": "मोबाइल समर्थन", "add_assessment": "आकलन जोड़ें", "add_questions": "आकलन प्रश्न जोड़ें", "_comment9": "add_user_validation.js", "validation.full_name_required": "पूरा नाम आवश्यक है", "validation.email_required": "ईमेल आवश्यक है", "validation.email_invalid": "कृपया एक मान्य ईमेल पता दर्ज करें", "validation.contact_required": "संपर्क नंबर आवश्यक है", "validation.contact_invalid": "कृपया एक मान्य 10-अंकीय फ़ोन नंबर दर्ज करें", "validation.dob_required": "जन्म तिथि आवश्यक है", "validation.dob_future": "जन्म तिथि भविष्य में नहीं हो सकती", "validation.user_role_required": "उपयोगकर्ता भूमिका आवश्यक है", "validation.profile_expiry_invalid": "प्रोफ़ाइल समाप्ति तिथि आज से पहले नहीं हो सकती", "validation.image_format": "केवल JPG और PNG फ़ाइलें अनुमत हैं", "validation.image_size": "फ़ाइल का आकार 5MB से अधिक नहीं होना चाहिए", "_comment10": "scorm_validation.js", "validation.scorm_title_required": "SCORM शीर्षक आवश्यक है", "validation.scorm_zip_required": "SCORM ज़िप फ़ाइल आवश्यक है", "validation.version_required": "संस्करण संख्या आवश्यक है।", "validation.scorm_category_required": "कृपया एक SCORM श्रेणी चुनें", "scorm.modal.edit": "SCORM पैकेज संपादित करें", "scorm.modal.add": "SCORM पैकेज जोड़ें", "validation.required.title": "शीर्षक आवश्यक है", "validation.required.content_type": "कृपया एक सामग्री प्रकार चुनें", "validation.required.version": "संस्करण आवश्यक है", "validation.required.tags": "टैग/कीवर्ड आवश्यक हैं", "validation.required.url": "URL आवश्यक है", "validation.invalid.url": "एक मान्य URL दर्ज करें (जैसे, https://example.com)", "error.form_not_found": "बाहरी सामग्री फ़ॉर्म नहीं मिला!", "error.submit_button_missing": "सबमिट बटन नहीं मिला! कृपया अपने HTML की जांच करें।", "validation.required.audio_file": "कृपया एक ऑडियो फ़ाइल अपलोड करें (MP3/WAV)", "validation.invalid.audio_file": "अमान्य फ़ाइल प्रकार। केवल MP3 और WAV फ़ाइलें अनुमत हैं।", "validation.required.thumbnail": "कृपया एक छवि फ़ाइल अपलोड करें (JPG, PNG, GIF, WebP)।", "validation.invalid.thumbnail": "अमान्य फ़ाइल प्रकार। केवल JPG, PNG, GIF, और WebP अनुमत हैं।", "validation.file_size_exceeded": "फ़ाइल आकार अनुमत सीमा से अधिक है।", "_comment12": "document_validation.js", "error.document_form_not_found": "दस्तावेज़ फ़ॉर्म नहीं मिला", "validation.document_title_required": "दस्तावेज़ शीर्षक आवश्यक है।", "validation.document_category_required": "दस्तावेज़ श्रेणी आवश्यक है।", "validation.document_file_required": "एक दस्तावेज़ फ़ाइल आवश्यक है।", "validation.invalid_file_format": "अमान्य फ़ाइल प्रारूप।", "validation.tags_required": "कम से कम एक टैग आवश्यक है।", "document.category.word_excel_ppt": "वर्ड/एक्सेल/पीपीटी फ़ाइलें", "document.category.ebook_manual": "ई-बुक और मैनुअल", "document.category.research_paper": "अनुसंधान पत्र और केस स्टडी", "_comment13": "document_package.js", "_comment14": "add_assessment.php", "confirm_delete_document": "क्या आप वाकई इस दस्तावेज़ सामग्री को हटाना चाहते हैं?", "no_documents_available": "कोई दस्तावेज़ उपलब्ध नहीं।", "confirm_delete_client": "क्या आप वाकई इस क्लाइंट को हटाना चाहते हैं?", "question_management_title": "मूल्यांकन प्रश्न प्रबंधन", "filters_select_options": "फ़िल्टर विकल्प चुनें", "filters_question_type": "प्रश्न का प्रकार", "filters_difficulty": "कठिनाई स्तर", "filters_tags": "टैग्स", "filters_created_by": "द्वारा बनाया गया", "filters_search": "खोजें", "buttons_add_question": "प्रश्न जोड़ें", "buttons_add_question_tooltip": "नया प्रश्न जोड़ने के लिए क्लिक करें", "buttons_import_user_tooltip": "फ़ाइल से उपयोगकर्ता आयात करने के लिए क्लिक करें", "question_grid_title": "शीर्षक", "question_grid_type": "प्र<PERSON><PERSON>र", "question_grid_difficulty": "कठिनाई स्तर", "question_grid_tags": "टैग्स", "question_grid_created_by": "द्वारा बनाया गया", "question_grid_actions": "क्रियाएं", "question_grid_edit": "इस प्रश्न को संपादित करें", "question_grid_delete": "इस प्रश्न को हटाएं", "question_grid_delete_confirm": "क्या आप वाकई इस प्रश्न को हटाना चाहते हैं?", "question_grid_no_questions_found": "कोई प्रश्न नहीं मिले।", "comment_16": "add_assessment_question.php", "edit_assessment_question_title": "मूल्यांकन प्रश्न संपादित करें", "add_assessment_question_title": "मूल्यांकन प्रश्न जोड़ें", "question_label": "प्रश्न", "type_and_press_enter": "टाइप करें और एंटर दबाएं...", "competency_skills": "क्षमता कौशल", "question_level": "प्रश्न स्तर", "low": "निम्न", "medium": "मध्यम", "hard": "कठिन", "marks_per_question": "प्रति प्रश्न अंक", "status": "स्थिति", "question_type": "प्रश्न प्रकार", "objective": "वस्तुनिष्ठ", "subjective": "वर्णनात्मक", "how_many_answer_options": "उत्तर विकल्पों की संख्या", "answer_options": "उत्तर विकल्प", "question_media_type": "प्रश्न मीडिया प्रकार", "text": "पाठ", "upload_media": "मीडिया अपलोड करें", "option": "विकल्प", "correct_answer": "सही उत्तर", "comment_17": "add_question_validation.js", "assessment.validation.required_field": "{field} आवश्यक है।", "assessment.validation.correct_answer_required": "कम से कम एक विकल्प को सही उत्तर के रूप में चिह्नित करना आवश्यक है।", "assessment.validation.media_required": "मीडिया अपलोड करना आवश्यक है।", "assessment.validation.invalid_media_type": "{type} के लिए केवल {allowed} फ़ाइलें ही अनुमत हैं।", "assessment.validation.media_size_exceeded": "फ़ाइल का आकार 5MB से अधिक नहीं होना चाहिए।", "assessment.validation.tags_required": "टैग/कीवर्ड आवश्यक हैं।", "comment_18": "assessment.php", "assessment.modal.add_title": "मूल्यांकन जोड़ें", "assessment.field.title": "मूल्यांकन शीर्षक", "assessment.field.num_attempts": "प्रयासों की संख्या", "assessment.field.passing_percentage": "उत्तीर्ण प्रतिशत (%)", "assessment.field.time_limit": "समय सीमा (मिनटों में)", "assessment.field.negative_marking": "नकारात्मक अंकन", "assessment.field.negative_percentage": "नकारात्मक अंकन प्रतिशत", "assessment.placeholder.select_negative_percentage": "नकारात्मक अंकन प्रतिशत चुनें", "assessment.field.type": "मूल्यांकन प्रकार", "assessment.type.fixed": "स्थिर", "assessment.type.dynamic": "गतिशील", "assessment.field.num_questions_to_display": "प्रदर्शित किए जाने वाले प्रश्नों की संख्या", "assessment.button.add_question": "प्रश्न जोड़ें", "_comment18a": "assessment_validation.js", "assessment.validation.title_required": "शीर्षक आवश्यक है।", "assessment.validation.passing_percentage_invalid": "उत्तीर्ण प्रतिशत 0 और 100 के बीच होना चाहिए।", "assessment.validation.negative_percentage_required": "प्रतिशत आवश्यक है।", "assessment.validation.num_questions_required": "इस फ़ील्ड के लिए संख्यात्मक मान आवश्यक है।", "assessment.validation.num_questions_exceeds": "{count} चयनित प्रश्न{plural} से अधिक नहीं हो सकता।", "assessment.validation.questions_required": "कम से कम एक प्रश्न चुना जाना चाहिए।", "assessment.table.question_title": "प्रश्न शीर्षक", "assessment.table.tags": "टैग्स / कीवर्ड्स", "assessment.table.marks": "अंक", "assessment.table.type": "प्र<PERSON><PERSON>र", "select_questions": "प्रश्न चुनें", "search_questions": "प्रश्न खोजें...", "loading": "लोड हो रहा है", "show_10": "10 दिखाएं", "show_25": "25 दिखाएं", "show_50": "50 दिखाएं", "show_75": "75 दिखाएं", "show_100": "100 दिखाएं", "clear_filters": "फ़िल्टर साफ़ करें", "refresh": "रीफ़्रेश करें", "question_title": "प्रश्न शीर्षक", "marks": "अंक", "type": "प्र<PERSON><PERSON>र", "loop_selected_questions": "चयनित प्रश्नों को लूप करें", "assessments": "मूल्यांकन", "delete_confirmation": "क्या आप वाकई इस मूल्यांकन को हटाना चाहते हैं?", "no_assessments_found": "कोई मूल्यांकन नहीं मिला।", "no_assessment_found": "कोई मूल्यांकन नहीं मिला।", "_comment19": "survey.php", "survey.modal.add_title": "सर्वेक्षण जोड़ें", "survey.modal.edit_title": "सर्वेक्षण संपादित करें", "survey.field.title": "सर्वेक्षण शीर्षक", "survey.button.add_question": "प्रश्न जोड़ें", "survey.table.question_title": "प्रश्न शीर्षक", "survey.table.tags": "टैग्स / कीवर्ड्स", "survey.table.type": "प्र<PERSON><PERSON>र", "survey.select_questions": "प्रश्न चुनें", "survey.question_title": "प्रश्न शीर्षक", "surveys": "सर्वेक्षण", "add_survey": "सर्वेक्षण जोड़ें", "add_survey_questions": "सर्वेक्षण प्रश्न जोड़ें", "no_surveys_found": "कोई सर्वेक्षण नहीं मिला।", "_comment19a": "add_survey.php", "survey.question_management_title": "सर्वेक्षण प्रश्न प्रबंधन", "survey.add_question_title": "सर्वेक्षण प्रश्न जोड़ें", "survey.edit_question_title": "सर्वेक्षण प्रश्न संपादित करें", "survey.question_title_field": "सर्वेक्षण प्रश्न शीर्षक", "survey.question_type": "प्रश्न प्रकार", "survey.rating_scale": "रेटिंग स्केल", "survey.rating_symbol": "रेटिंग प्रतीक", "survey.multi_choice": "बहुविकल्पीय", "survey.checkbox": "चेकबॉक्स", "survey.short_answer": "छोटा उत्तर", "survey.long_answer": "लंबा उत्तर", "survey.dropdown": "ड्रॉपडाउन", "survey.upload": "अपलोड", "survey.rating": "रेटिंग", "survey.option": "विकल्प", "survey.add_option": "विकल्प जोड़ें", "survey.remove_option": "विकल्प हटाएं", "survey.submit_question": "सर्वेक्षण प्रश्न सबमिट करें", "survey.question_saved": "सर्वेक्षण प्रश्न सफलतापूर्वक सहेजा गया।", "survey.question_updated": "सर्वेक्षण प्रश्न सफलतापूर्वक अपडेट किया गया।", "survey.question_deleted": "सर्वेक्षण प्रश्न सफलतापूर्वक हटाया गया।", "survey_question_management_title": "सर्वेक्षण प्रश्न प्रबंधन", "buttons_add_survey_question": "सर्वेक्षण प्रश्न जोड़ें", "buttons_import_survey": "सर्वेक्षण आयात करें", "buttons_import_survey_tooltip": "फ़ाइल से सर्वेक्षण आयात करने के लिए क्लिक करें", "survey_question_title": "सर्वेक्षण प्रश्न शीर्षक", "survey_question_type": "प्रश्न प्रकार", "survey_rating_scale": "रेटिंग स्केल", "symbol_star": "स्टार", "symbol_thumb": "थंब", "symbol_heart": "हार्ट", "question_type_multi_choice": "बहुविकल्पीय", "question_type_checkbox": "चेकबॉक्स", "question_type_short_answer": "छोटा उत्तर", "question_type_long_answer": "लंबा उत्तर", "question_type_dropdown": "ड्रॉपडाउन", "question_type_upload": "अपलोड", "question_type_rating": "रेटिंग", "buttons_submit_survey_question": "सर्वेक्षण प्रश्न सबमिट करें", "survey_grid_title": "शीर्षक", "survey_grid_type": "प्र<PERSON><PERSON>र", "survey_grid_tags": "टैग्स", "survey_grid_actions": "क्रियाएं", "survey_grid_edit": "इस प्रश्न को संपादित करें", "survey_grid_delete": "इस प्रश्न को हटाएं", "survey_grid_delete_confirm": "क्या आप वाकई इस प्रश्न को हटाना चाहते हैं?", "survey_grid_no_questions_found": "कोई प्रश्न नहीं मिले।", "_comment20": "feedback.php", "feedback.modal.add_title": "फीडबैक जोड़ें", "feedback.modal.edit_title": "फीडबैक संपादित करें", "feedback.field.title": "फीडबैक शीर्षक", "feedback.button.add_question": "प्रश्न जोड़ें", "feedback.table.question_title": "प्रश्न शीर्षक", "feedback.table.tags": "टैग्स / कीवर्ड्स", "feedback.table.type": "प्र<PERSON><PERSON>र", "feedback.select_questions": "प्रश्न चुनें", "feedback.question_title": "प्रश्न शीर्षक", "feedbacks": "फीडबैक", "add_feedback": "फीडबैक जोड़ें", "add_feedback_questions": "फीडबैक प्रश्न जोड़ें", "no_feedback_found": "कोई फीडबैक नहीं मिला।", "_comment20a": "add_feedback_question.php", "feedback.question_management_title": "फीडबैक प्रश्न प्रबंधन", "feedback.add_question_title": "फीडबैक प्रश्न जोड़ें", "feedback.edit_question_title": "फीडबैक प्रश्न संपादित करें", "feedback.question_title_field": "फीडबैक प्रश्न शीर्षक", "feedback.question_type": "प्रश्न प्रकार", "feedback.rating_scale": "रेटिंग स्केल", "feedback.rating_symbol": "रेटिंग प्रतीक", "feedback.multi_choice": "बहुविकल्पीय", "feedback.checkbox": "चेकबॉक्स", "feedback.short_answer": "छोटा उत्तर", "feedback.long_answer": "लंबा उत्तर", "feedback.dropdown": "ड्रॉपडाउन", "feedback.upload": "अपलोड", "feedback.rating": "रेटिंग", "feedback.option": "विकल्प", "feedback.add_option": "विकल्प जोड़ें", "feedback.remove_option": "विकल्प हटाएं", "feedback.submit_question": "फीडबैक प्रश्न सबमिट करें", "feedback.question_saved": "फीडबैक प्रश्न सफलतापूर्वक सहेजा गया।", "feedback.question_updated": "फीडबैक प्रश्न सफलतापूर्वक अपडेट किया गया।", "feedback.question_deleted": "फीडबैक प्रश्न सफलतापूर्वक हटाया गया।", "feedback_question_management_title": "फीडबैक प्रश्न प्रबंधन", "buttons_add_feedback_question": "फीडबैक प्रश्न जोड़ें", "buttons_import_feedback": "फीडबैक आयात करें", "buttons_import_feedback_tooltip": "फ़ाइल से फीडबैक आयात करने के लिए क्लिक करें", "feedback_question_title": "फीडबैक प्रश्न शीर्षक", "feedback_question_type": "प्रश्न प्रकार", "feedback_rating_scale": "रेटिंग स्केल", "upload_image_video_pdf": "छवि, वीडियो, या PDF अपलोड करें", "validation_required": "यह फ़ील्ड आवश्यक है", "buttons_submit_feedback_question": "फीडबैक प्रश्न सबमिट करें", "buttons_cancel": "रद्<PERSON> करें", "buttons_close": "ब<PERSON><PERSON> करें", "add_tag": "टैग जोड़ें", "feedback_grid_title": "शीर्षक", "feedback_grid_type": "प्र<PERSON><PERSON>र", "feedback_grid_tags": "टैग्स", "feedback_grid_actions": "क्रियाएं", "feedback_grid_edit": "इस प्रश्न को संपादित करें", "feedback_grid_delete": "इस प्रश्न को हटाएं", "feedback_grid_delete_confirm": "क्या आप वाकई इस प्रश्न को हटाना चाहते हैं?", "feedback_grid_no_questions_found": "कोई प्रश्न नहीं मिले।", "_comment21": "audio.php", "audio.modal.add_title": "ऑडियो जोड़ें", "audio.modal.edit_title": "ऑडियो संपादित करें", "audio.field.title": "ऑडियो शीर्षक", "audio.field.file": "ऑडियो फ़ाइल", "audio.field.duration": "अवधि", "audio.field.speaker": "स्पीकर", "audios": "ऑडियो", "add_audio": "ऑडियो जोड़ें", "add_audio_package": "ऑडियो पैकेज जोड़ें", "no_audio_found": "कोई ऑडियो नहीं मिला।", "audio.package_saved": "ऑडियो पैकेज सफलतापूर्वक सहेजा गया।", "audio.package_updated": "ऑडियो पैकेज सफलतापूर्वक अपडेट किया गया।", "audio.package_deleted": "ऑडियो पैकेज सफलतापूर्वक हटाया गया।", "audio.upload_audio_file": "ऑडियो फ़ाइल अपलोड करें", "audio.select_audio_file": "ऑडियो फ़ाइल चुनें", "_comment22": "video.php", "video.modal.add_title": "वीडियो जोड़ें", "video.modal.edit_title": "वीडियो संपादित करें", "video.field.title": "वीडियो शीर्षक", "video.field.file": "वीडियो फ़ाइल", "video.field.duration": "अवधि", "video.field.resolution": "रिज़ॉल्यूशन", "videos": "वीडियो", "add_video": "वीडियो जोड़ें", "add_video_package": "वीडियो पैकेज जोड़ें", "no_videos_found": "कोई वीडियो नहीं मिला।", "no_video_found": "कोई वीडियो नहीं मिला।", "video.package_saved": "वीडियो पैकेज सफलतापूर्वक सहेजा गया।", "video.package_updated": "वीडियो पैकेज सफलतापूर्वक अपडेट किया गया।", "video.package_deleted": "वीडियो पैकेज सफलतापूर्वक हटाया गया।", "video.upload_video_file": "वीडियो फ़ाइल अपलोड करें", "video.select_video_file": "वीडियो फ़ाइल चुनें", "_comment23": "image.php", "image.modal.add_title": "छवि जोड़ें", "image.modal.edit_title": "छवि संपादित करें", "image.field.title": "छवि शीर्षक", "image.field.file": "छवि फ़ाइल", "image.field.resolution": "रिज़ॉल्यूशन", "image.field.alt_text": "वैकल्पिक टेक्स्ट", "images": "छवियां", "add_image": "छवि जोड़ें", "add_image_package": "छवि पैकेज जोड़ें", "no_images_found": "कोई छवि नहीं मिली।", "no_image_found": "कोई छवि नहीं मिली।", "_comment27": "सामग्री पूर्वावलोकन संदेश", "add_interactive_content": "इंटरैक्टिव सामग्री जोड़ें", "add_interactive_package": "इंटरैक्टिव पैकेज जोड़ें", "interactive_ai_tutoring": "एआई ट्यूटरिंग", "interactive_adaptive_learning_desc": "व्यक्तिगत शिक्षार्थी आवश्यकताओं के अनुकूल व्यक्तिगत शिक्षण अनुभव", "interactive_ai_tutoring_desc": "व्यक्तिगत मार्गदर्शन के लिए एआई-संचालित ट्यूटरिंग सिस्टम", "interactive_ar_vr_desc": "संवर्धित और आभासी वास्तविकता इमर्सिव शिक्षण अनुभव", "interactive.field.title": "सामग्री शीर्षक", "interactive.field.content_type": "सामग्री प्रकार", "interactive.field.description": "विवरण", "interactive.field.version": "संस्करण", "interactive.field.language": "भाषा", "interactive.field.time_limit": "समय सीमा (मिनट)", "interactive.field.mobile_support": "मोबाइल समर्थन", "interactive.field.content_url": "सामग्री URL", "interactive.field.embed_code": "एम्बेड कोड", "interactive.field.ai_model": "एआई मॉडल", "interactive.field.interaction_type": "इंटरैक्शन प्रकार", "interactive.field.difficulty_level": "कठिनाई स्तर", "interactive.field.learning_objectives": "शिक्षण उद्देश्य", "interactive.field.prerequisites": "पूर्वापेक्षाएं", "interactive.field.content_file": "सामग्री फ़ाइल", "interactive.field.thumbnail_image": "थंबनेल छवि", "interactive.field.vr_platform": "VR प्लेटफॉर्म", "interactive.field.ar_platform": "AR प्लेटफॉर्म", "interactive.field.device_requirements": "डिवाइस आवश्यकताएं", "interactive.field.tutor_personality": "ट्यूटर व्यक्तित्व", "interactive.field.response_style": "प्रतिक्रिया शैली", "interactive.field.knowledge_domain": "ज्ञान डोमेन", "interactive.field.adaptation_algorithm": "अनुकूलन एल्गोरिदम", "interactive.field.assessment_integration": "मूल्यांकन एकीकरण", "interactive.field.progress_tracking": "प्रगति ट्रैकिंग", "interactive.placeholder.select_content_type": "सामग्री प्रकार चुनें", "interactive.placeholder.select_difficulty": "कठिनाई स्तर चुनें", "interactive.placeholder.select_ai_model": "एआई मॉडल चुनें", "interactive.placeholder.select_vr_platform": "VR प्लेटफॉर्म चुनें", "interactive.placeholder.select_ar_platform": "AR प्लेटफॉर्म चुनें", "interactive.placeholder.select_tutor_personality": "ट्यूटर व्यक्तित्व चुनें", "interactive.placeholder.select_response_style": "प्रतिक्रिया शैली चुनें", "interactive.placeholder.select_adaptation_algorithm": "अनुकूलन एल्गोरिदम चुनें", "interactive.difficulty.beginner": "शुरुआती", "interactive.difficulty.intermediate": "मध्यम", "interactive.difficulty.advanced": "उन्नत", "interactive.ai_model.gpt4": "GPT-4", "interactive.ai_model.claude": "<PERSON>", "interactive.ai_model.gemini": "Gemini", "interactive.ai_model.custom": "कस्टम मॉडल", "interactive.vr_platform.oculus": "Oculus", "interactive.vr_platform.htc_vive": "HTC Vive", "interactive.vr_platform.playstation_vr": "PlayStation VR", "interactive.vr_platform.web_vr": "WebVR", "interactive.ar_platform.arcore": "ARCore", "interactive.ar_platform.arkit": "ARKit", "interactive.ar_platform.web_ar": "WebAR", "interactive.tutor_personality.friendly": "मित्रवत", "interactive.tutor_personality.professional": "पेशेवर", "interactive.tutor_personality.encouraging": "प्रोत्साहनकारी", "interactive.tutor_personality.strict": "सख्त", "interactive.response_style.formal": "औपचारिक", "interactive.response_style.casual": "अनौपचारिक", "interactive.response_style.conversational": "बातचीत", "interactive.adaptation_algorithm.bayesian": "बायेसियन नॉलेज ट्रेसिंग", "interactive.adaptation_algorithm.irt": "आइटम रिस्पांस थ्योरी", "interactive.adaptation_algorithm.ml": "मशीन लर्निंग", "interactive.interaction_type.chat": "चैट इंटरफेस", "interactive.interaction_type.simulation": "सिमुलेशन", "interactive.interaction_type.game": "गेमिफिकेशन", "interactive.interaction_type.quiz": "इंटरैक्टिव क्विज़", "no_interactive_content_found": "कोई इंटरैक्टिव सामग्री नहीं मिली।", "image.package_saved": "छवि पैकेज सफलतापूर्वक सहेजा गया।", "image.package_updated": "छवि पैकेज सफलतापूर्वक अपडेट किया गया।", "image.package_deleted": "छवि पैकेज सफलतापूर्वक हटाया गया।", "image.upload_image_file": "छवि फ़ाइल अपलोड करें", "image.select_image_file": "छवि फ़ाइल चुनें", "_comment24": "Package-specific validation messages", "survey.validation.title_required": "सर्वेक्षण शीर्षक आवश्यक है।", "survey.validation.question_type_required": "सर्वेक्षण प्रश्न प्रकार आवश्यक है।", "feedback.validation.title_required": "फीडबैक शीर्षक आवश्यक है।", "feedback.validation.question_type_required": "फीडबैक प्रश्न प्रकार आवश्यक है।", "audio.validation.title_required": "ऑडियो शीर्षक आवश्यक है।", "audio.validation.file_required": "ऑडियो फ़ाइल आवश्यक है।", "video.validation.title_required": "वीडियो शीर्षक आवश्यक है।", "video.validation.file_required": "वीडियो फ़ाइल आवश्यक है।", "image.validation.title_required": "छवि शीर्षक आवश्यक है।", "image.validation.file_required": "छवि फ़ाइल आवश्यक है।", "_comment25": "Common success/error messages", "success.saved": "सफलतापूर्वक सहेजा गया!", "success.updated": "सफलतापूर्वक अपडेट किया गया!", "success.deleted": "सफलतापूर्वक हटाया गया!", "error.save_failed": "सहेजने में विफल।", "error.update_failed": "अपडेट करने में विफल।", "error.delete_failed": "हटाने में विफल।", "error.not_found": "आइटम नहीं मिला।", "error.unauthorized": "अनधिकृत पहुंच।", "_comment_confirmations": "पुष्टिकरण मोडल संदेश", "confirmation.delete.title": "हटाने की पुष्टि", "confirmation.lock.title": "लॉक करने की पुष्टि", "confirmation.unlock.title": "अनलॉक करने की पुष्टि", "confirmation.delete.message": "क्या आप वाकई इस {item} को हटाना चाहते हैं?", "confirmation.lock.message": "क्या आप वाकई इस {item} को लॉक करना चाहते हैं?", "confirmation.unlock.message": "क्या आप वाकई इस {item} को अनलॉक करना चाहते हैं?", "confirmation.delete.subtext": "यह क्रिया पुनः संभव नहीं है।", "confirmation.lock.subtext": "इससे उपयोगकर्ता लॉग इन नहीं कर पाएगा।", "confirmation.unlock.subtext": "इससे उपयोगकर्ता फिर से लॉग इन कर पाएगा।", "confirmation.delete.button": "हटाएं", "confirmation.lock.button": "लॉक करें", "confirmation.unlock.button": "अनलॉक करें", "confirmation.cancel.button": "रद्<PERSON> करें", "_comment_items": "पुष्टिकरण के लिए आइटम नाम", "item.user": "उपयोगकर्ता \"{name}\"", "item.scorm_package": "SCORM पैकेज \"{title}\"", "item.non_scorm_package": "गैर-SCORM पैकेज \"{title}\"", "item.assessment": "मूल्यांकन \"{title}\"", "item.audio_package": "ऑडियो पैकेज \"{title}\"", "item.video_package": "वीडियो पैकेज \"{title}\"", "item.image_package": "छवि पैकेज \"{title}\"", "item.document": "दस्तावेज़ \"{title}\"", "item.external_content": "बाहरी सामग्री \"{title}\"", "item.interactive_content": "इंटरएक्टिव सामग्री \"{title}\"", "item.survey": "सर्वेक्षण \"{title}\"", "item.feedback": "फीडबैक \"{title}\"", "item.question": "प्रश्न \"{title}\"", "item.assessment_question": "मूल्यांकन प्रश्न \"{title}\"", "item.survey_question": "सर्वेक्षण प्रश्न \"{title}\"", "item.feedback_question": "फीडबैक प्रश्न \"{title}\"", "item.organization": "संगठन \"{name}\"", "organization_management": "संगठन प्रबंधन", "add_organization": "संगठन जोड़ें", "search_organizations": "संगठन खोजें...", "all_statuses": "सभी स्थितियां", "no_organizations_found": "कोई संगठन नहीं मिला", "client_code": "क्लाइंट कोड", "users": "उपयोगकर्ता", "plan": "योजना", "created": "बनाया गया", "manage_users": "उपयोगकर्ता प्रबंधन", "analytics": "विश्लेषण", "error.invalid_request": "अमान्य अनुरोध।", "_comment26": "Client Management", "client_management": "क्लाइंट प्रबंधन", "client_management_title": "क्लाइंट प्रबंधन", "client_management_description": "क्लाइंट संगठनों और उनकी सेटिंग्स का प्रबंधन करें", "clients_all_statuses": "सभी स्थितियां", "clients_active": "सक्रिय", "clients_inactive": "निष्क्रिय", "clients_suspended": "निलंबित", "clients_search_placeholder": "क्लाइंट खोजें...", "clients_search_title": "क्लाइंट खोजें", "clients_clear_filters": "फिल्टर साफ़ करें", "clients_clear_filters_title": "सभी फिल्टर साफ़ करें", "clients_add_client": "क्लाइंट जोड़ें", "clients_add_client_title": "नया क्लाइंट जोड़ें", "clients_no_clients_found": "कोई क्लाइंट नहीं मिला", "clients_loading": "क्लाइंट लोड हो रहे हैं...", "clients_client_id": "क्लाइंट आईडी", "clients_users": "उपयोगकर्ता", "clients_admin_limit": "एडमिन सीमा", "clients_roles": "भूमिकाएं", "clients_features": "सुविधाएं", "clients_reports": "रिपोर्ट", "clients_themes": "थीम", "clients_sso": "एसएसओ", "clients_created": "बनाया गया", "clients_edit_title": "संपादित करें", "clients_manage_users_title": "उपयोगकर्ता प्रबंधन", "clients_statistics_title": "आंकड़े", "clients_delete_title": "हटाएं", "clients_pagination_label": "क्लाइंट पेजिनेशन", "clients_add_modal_title": "नया क्लाइंट जोड़ें", "clients_edit_modal_title": "क्लाइंट संपादित करें", "clients_basic_information": "मूलभूत जानकारी", "clients_client_name": "क्लाइंट नाम", "clients_client_name_required": "क्लाइंट नाम *", "clients_client_code": "क्लाइंट कोड", "clients_client_code_required": "क्लाइंट कोड *", "clients_client_code_placeholder": "उदा., ACME_CORP", "clients_client_code_help": "क्लाइंट के लिए अद्वितीय पहचानकर्ता (बड़े अक्षर, कोई स्थान नहीं, अंडरस्कोर का उपयोग करें)", "clients_custom_field_creation": "कस्टम फील्ड निर्माण", "clients_custom_field_creation_help": "इस क्लाइंट को उपयोगकर्ता प्रोफाइल के लिए कस्टम फील्ड बनाने की अनुमति दें", "clients_maximum_users": "अधिकतम उपयोगकर्ता", "clients_maximum_users_required": "अधिकतम उपयोगकर्ता *", "clients_maximum_users_placeholder": "उपयोगकर्ताओं की संख्या दर्ज करें", "clients_status": "स्थिति", "clients_configuration_settings": "कॉन्फ़िगरेशन सेटिंग्स", "clients_reports_enabled": "रिपोर्ट", "clients_theme_color_setting": "थीम रंग सेटिंग", "clients_sso_login": "एसएसओ लॉगिन", "clients_admin_role_limit": "एडमिन भूमिका सीमा", "clients_admin_role_limit_required": "एडमिन भूमिका सीमा *", "clients_admin_role_limit_placeholder": "अनुमतित एडमिन भूमिकाओं की संख्या दर्ज करें", "clients_admin_role_limit_help": "अनुमतित एडमिन उपयोगकर्ताओं की अधिकतम संख्या", "clients_branding": "ब्रांडिंग", "clients_client_logo": "क्लाइंट लोगो", "clients_client_logo_required": "क्लाइंट लोगो *", "clients_logo_help": "<PERSON><PERSON>, <PERSON><PERSON>, या GIF अपलोड करें (अधिकतम 5MB)", "clients_logo_help_edit": "<PERSON><PERSON>, <PERSON><PERSON>, या GIF अपलोड करें (अधिकतम 5MB) - वर्तमान लोगो रखने के लिए खाली छोड़ें", "clients_description": "विवरण", "clients_description_placeholder": "क्लाइंट का संक्षिप्त विवरण...", "clients_current_logo": "वर्तमान लोगो:", "clients_yes": "हां", "clients_no": "नहीं", "clients_cancel": "रद्<PERSON> करें", "clients_create_client": "क्लाइंट बनाएं", "clients_update_client": "क्लाइंट अपडेट करें", "clients_close": "ब<PERSON><PERSON> करें", "custom_fields_management": "कस्टम फील्ड प्रबंधन", "custom_fields_title": "कस्टम फील्ड प्रबंधित करें", "custom_fields_description": "उपयोगकर्ता प्रोफाइल के लिए कस्टम फील्ड बनाएं और प्रबंधित करें", "custom_fields_create_button": "कस्टम फील्ड बनाएं", "custom_fields_create_title": "कस्टम फील्ड बनाएं", "custom_fields_edit_title": "कस्टम फील्ड संपादित करें", "custom_fields_field_name": "फील्ड नाम", "custom_fields_field_name_required": "फील्ड नाम *", "custom_fields_field_name_placeholder": "फील्ड नाम दर्ज करें (जैसे, employee_id)", "custom_fields_field_label": "फील्ड लेबल", "custom_fields_field_label_required": "फील्ड लेबल *", "custom_fields_field_label_placeholder": "फील्ड लेबल दर्ज करें (जैसे, कर्मचारी आईडी)", "custom_fields_field_type": "फील्ड प्रकार", "custom_fields_field_type_required": "फील्ड प्रकार *", "custom_fields_field_type_text": "टेक्स्ट इनपुट", "custom_fields_field_type_textarea": "टेक्स्ट एरिया", "custom_fields_field_type_select": "ड्रॉपडाउन", "custom_fields_field_type_radio": "रेडियो बटन", "custom_fields_field_type_checkbox": "चेकबॉक्स", "custom_fields_field_type_file": "फाइल अपलोड", "custom_fields_field_type_date": "डेट पिकर", "custom_fields_field_type_number": "नंबर इनपुट", "custom_fields_field_type_email": "ईमेल इनपुट", "custom_fields_field_type_phone": "फोन इनपुट", "custom_fields_field_options": "फील्ड विकल्प", "custom_fields_field_options_placeholder": "विकल्प दर्ज करें (प्रति लाइन एक)\nविकल्प 1\nविकल्प 2\nविकल्प 3", "custom_fields_field_options_help": "प्रत्येक विकल्प को नई लाइन में दर्ज करें (ड्रॉपडाउन, रेडियो, चेकबॉक्स फील्ड के लिए)", "custom_fields_is_required": "आवश्यक फील्ड", "custom_fields_is_active": "सक्रिय", "custom_fields_no_fields": "कोई कस्टम फील्ड नहीं मिला", "custom_fields_no_fields_description": "शुरुआत करने के लिए अपना पहला कस्टम फील्ड बनाएं", "custom_fields_actions": "क्रियाएं", "custom_fields_edit": "संपादित करें", "custom_fields_delete": "हटाएं", "custom_fields_delete_confirm": "क्या आप वाकई इस कस्टम फील्ड को हटाना चाहते हैं?", "custom_fields_delete_warning": "इससे इस फील्ड के लिए सभी उपयोगकर्ता डेटा भी हट जाएगा।", "all_clients": "सभी क्लाइंट", "all_types": "सभी प्रकार", "all_status": "सभी स्थिति", "all_fields": "सभी फील्ड", "active_fields": "सक्रिय फील्ड", "inactive_fields": "निष्क्रिय फील्ड", "_comment28": "Client Management Validation", "js.validation.client_name_required": "क्लाइंट नाम आवश्यक है।", "js.validation.client_code_required": "क्लाइंट कोड आवश्यक है।", "js.validation.client_code_format": "क्लाइंट कोड में केवल बड़े अक्षर, संख्याएं और अंडरस्कोर होने चाहिए।", "js.validation.max_users_required": "अधिकतम उपयोगकर्ता आवश्यक है।", "js.validation.max_users_numeric": "अधिकतम उपयोगकर्ता एक संख्या होना चाहिए।", "js.validation.max_users_minimum": "अधिकतम उपयोगकर्ता कम से कम 1 होना चाहिए।", "js.validation.admin_role_limit_required": "एडमिन भूमिका सीमा आवश्यक है।", "js.validation.admin_role_limit_numeric": "एडमिन भूमिका सीमा एक संख्या होनी चाहिए।", "js.validation.admin_role_limit_minimum": "एडमिन भूमिका सीमा कम से कम 1 होनी चाहिए।", "js.validation.client_logo_required": "क्लाइंट लोगो आवश्यक है।", "js.validation.logo_format_invalid": "लोगो PNG, <PERSON><PERSON>, या GIF प्रारूप में होना चाहिए।", "js.validation.logo_size_exceeded": "लोगो फ़ाइल का आकार 5MB से कम होना चाहिए।", "js.validation.client_form_not_found": "क्लाइंट फॉर्म नहीं मिला!", "validation.client_name_required": "क्लाइंट नाम आवश्यक है।", "validation.client_code_required": "क्लाइंट कोड आवश्यक है।", "validation.client_code_unique": "क्लाइंट कोड पहले से मौजूद है। कृपया एक अलग कोड चुनें।", "validation.client_code_format": "क्लाइंट कोड में केवल बड़े अक्षर, संख्याएं और अंडरस्कोर होने चाहिए।", "validation.max_users_required": "अधिकतम उपयोगकर्ता आवश्यक है।", "validation.max_users_numeric": "अधिकतम उपयोगकर्ता एक संख्या होना चाहिए।", "validation.max_users_minimum": "अधिकतम उपयोगकर्ता कम से कम 1 होना चाहिए।", "validation.admin_role_limit_required": "एडमिन भूमिका सीमा आवश्यक है।", "validation.admin_role_limit_numeric": "एडमिन भूमिका सीमा एक संख्या होनी चाहिए।", "validation.admin_role_limit_minimum": "एडमिन भूमिका सीमा कम से कम 1 होनी चाहिए।", "validation.logo_upload_failed": "लोगो अपलोड करने में विफल। कृपया पुनः प्रयास करें।", "validation.client_id_required": "क्लाइंट आईडी आवश्यक है।", "validation.client_not_found": "क्लाइंट नहीं मिला।", "error.client_update_failed": "क्लाइंट अपडेट करते समय एक अप्रत्याशित त्रुटि हुई।", "error.client_delete_failed": "क्लाइंट हटाने में विफल।", "success.client_created": "क्लाइंट सफलतापूर्वक बनाया गया!", "success.client_updated": "क्लाइंट सफलतापूर्वक अपडेट किया गया!", "success.client_deleted": "क्लाइंट सफलतापूर्वक हटाया गया!", "validation.field_name_required": "फील्ड नाम आवश्यक है।", "validation.field_label_required": "फील्ड लेबल आवश्यक है।", "validation.field_type_required": "फील्ड प्रकार आवश्यक है।", "validation.field_name_exists": "इस नाम के साथ एक फील्ड पहले से मौजूद है।", "validation.field_id_required": "फील्ड आईडी आवश्यक है।", "validation.field_not_found": "कस्टम फील्ड नहीं मिला।", "success.custom_field_created": "कस्टम फील्ड सफलतापूर्वक बनाया गया!", "success.custom_field_updated": "कस्टम फील्ड सफलतापूर्वक अपडेट किया गया!", "success.custom_field_deleted": "कस्टम फील्ड सफलतापूर्वक हटाया गया!", "error.custom_field_create_failed": "कस्टम फील्ड बनाने में विफल।", "error.custom_field_update_failed": "कस्टम फील्ड अपडेट करने में विफल।", "error.custom_field_delete_failed": "कस्टम फील्ड हटाने में विफल।", "_comment29": "JavaScript validation messages", "js.validation.title_required": "शीर्षक आवश्यक है।", "js.validation.feedback_title_required": "फीडबैक शीर्षक आवश्यक है।", "js.validation.survey_title_required": "सर्वेक्षण शीर्षक आवश्यक है।", "js.validation.feedback_question_title_required": "फीडबैक प्रश्न शीर्षक आवश्यक है।", "js.validation.survey_question_title_required": "सर्वेक्षण प्रश्न शीर्षक आवश्यक है।", "js.validation.tags_required": "कम से कम एक टैग आवश्यक है।", "js.validation.questions_required": "कृपया कम से कम एक फीडबैक प्रश्न जोड़ें।", "js.validation.survey_questions_required": "कृपया कम से कम एक सर्वेक्षण प्रश्न जोड़ें।", "js.validation.option_empty": "विकल्प खाली नहीं हो सकता।", "js.validation.option_required": "कम से कम एक विकल्प आवश्यक है।", "js.validation.audio_title_required": "ऑडियो शीर्षक आवश्यक है।", "js.validation.audio_file_required": "ऑडियो फ़ाइल आवश्यक है।", "js.validation.audio_file_size_exceeded": "ऑडियो फ़ाइल का आकार अधिकतम अनुमत सीमा 10MB से अधिक है।", "js.validation.video_title_required": "वीडियो शीर्षक आवश्यक है।", "js.validation.video_file_required": "वीडियो फ़ाइल आवश्यक है।", "js.validation.video_file_size_exceeded": "वीडियो फ़ाइल का आकार अधिकतम अनुमत सीमा 500MB से अधिक है।", "js.validation.image_title_required": "छवि शीर्षक आवश्यक है।", "js.validation.image_file_required": "छवि फ़ाइल आवश्यक है।", "js.validation.image_file_size_exceeded": "छवि फ़ाइल का आकार अधिकतम अनुमत सीमा 50MB से अधिक है।", "js.validation.interactive_title_required": "इंटरैक्टिव सामग्री शीर्षक आवश्यक है।", "js.validation.interactive_content_type_required": "सामग्री प्रकार आवश्यक है।", "js.validation.interactive_version_required": "संस्करण आवश्यक है।", "js.validation.interactive_tags_required": "टैग आवश्यक हैं।", "js.validation.interactive_url_invalid": "कृपया एक मान्य URL दर्ज करें (जैसे, https://example.com)।", "js.validation.version_required_numeric": "संस्करण एक मान्य संख्या होना चाहिए।", "js.validation.time_limit_numeric": "समय सीमा एक मान्य संख्या होनी चाहिए।", "validation.required.field": "यह फ़ील्ड आवश्यक है।", "html5_content": "HTML5 सामग्री", "flash_content": "<PERSON> सामग्री", "unity_content": "Unity सामग्री", "custom_web_app": "कस्टम वेब ऐप", "mobile_app": "मोबाइल ऐप", "no_nonscorm_content_found": "कोई नॉन-स्कॉर्म सामग्री नहीं मिली।", "no_html5_content_found": "कोई HTML5 सामग्री नहीं मिली।", "no_flash_content_found": "कोई Flash सामग्री नहीं मिली।", "no_unity_content_found": "कोई Unity सामग्री नहीं मिली।", "no_custom_web_found": "कोई कस्टम वेब ऐप नहीं मिला।", "no_mobile_app_found": "कोई मोबाइल ऐप नहीं मिला।", "js.validation.invalid_flash_version": "कृपया एक वैध फ्लैश संस्करण दर्ज करें (जैसे, 11.2.0)।", "js.validation.invalid_unity_version": "कृपया एक वैध यूनिटी संस्करण दर्ज करें (जैसे, 2022.3.0f1)।", "js.validation.invalid_os_version": "कृपया एक वैध OS संस्करण दर्ज करें (जैसे, iOS 14.0, Android 8.0)।", "js.validation.version_required": "संस्करण आवश्यक है।", "js.validation.content_type_required": "सामग्री प्रकार आवश्यक है।", "js.validation.nonscorm_title_required": "नॉन-स्कॉर्म शीर्षक आवश्यक है।", "js.validation.nonscorm_tags_required": "टैग आवश्यक हैं।", "js.validation.nonscorm_file_required": "फ़ाइल आवश्यक है।", "js.validation.nonscorm_url_required": "URL आवश्यक है।", "js.validation.nonscorm_url_invalid": "कृपया एक वैध URL दर्ज करें (जैसे, https://example.com)।", "js.validation.flash_version_required": "फ्लैश संस्करण आवश्यक है।", "js.validation.unity_version_required": "यूनिटी संस्करण आवश्यक है।", "js.validation.mobile_platform_required": "मोबाइल प्लेटफॉर्म आवश्यक है।", "js.validation.app_store_url_invalid": "कृपया एक वैध ऐप स्टोर URL दर्ज करें।", "js.validation.minimum_os_version_required": "न्यूनतम OS संस्करण आवश्यक है।", "preview.audio_title": "ऑडियो पूर्वावलोकन", "preview.video_title": "वीडियो पूर्वावलोकन", "preview.image_title": "छवि पूर्वावलोकन", "preview.document_title": "दस्तावेज़ पूर्वावलोकन", "preview.external_title": "बाहरी सामग्री पूर्वावलोकन", "preview.no_description": "कोई विवरण उपलब्ध नहीं", "preview.version": "संस्करण", "preview.language": "भाषा", "preview.mobile_support": "मोबाइल समर्थन", "preview.category": "श्रेणी", "preview.type": "प्र<PERSON><PERSON>र", "preview.no_file_available": "कोई फ़ाइल उपलब्ध नहीं", "preview.no_associated_file": "इस दस्तावेज़ की कोई संबद्ध फ़ाइल नहीं है।", "preview.open_new_tab": "नए टैब में खोलें", "preview.document_preview_not_available": "दस्तावेज़ पूर्वावलोकन उपलब्ध नहीं", "preview.cannot_preview_browser": "ब्राउज़र में सीधे पूर्वावलोकन नहीं किया जा सकता।", "preview.download_document": "दस्तावेज़ डाउनलोड करें", "preview.unsupported_file_type": "असमर्थित फ़ाइल प्रकार", "preview.preview_not_available_filetype": "इस फ़ाइल प्रकार के लिए पूर्वावलोकन उपलब्ध नहीं", "preview.download_file": "फ़ाइल डाउनलोड करें", "preview.video_not_available": "वीडियो पूर्वावलोकन उपलब्ध नहीं।", "preview.open_video": "वीडियो खोलें", "preview.course_content": "पाठ्यक्रम सामग्री", "preview.platform": "प्लेटफ़ॉर्म", "preview.open_course": "पाठ्यक्रम खोलें", "preview.web_article_blog": "वेब लेख/ब्लॉग", "preview.author": "लेखक", "preview.read_article": "लेख पढ़ें", "preview.podcast_audio_content": "पॉडकास्ट/ऑडियो सामग्री", "preview.speaker": "स्पीकर", "preview.listen_audio": "ऑडियो सुनें", "preview.preview_not_available": "पूर्वावलोकन उपलब्ध नहीं", "preview.content_type_cannot_preview": "इस सामग्री प्रकार का पूर्वावलोकन नहीं किया जा सकता।", "preview.unknown": "अज्ञात", "preview.image_not_found": "छवि नहीं मिली"}