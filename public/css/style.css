/* Unlock Your Skills - Complete Navbar, Sidebar, and Manage Portal CSS */

/* ✅ Fix Body Layout */
body {
    background-color: #f4f4f4;
    font-family: Arial, sans-serif;
    margin-top: 60px;
    overflow-x: hidden;
}

/* ✅ Prevent Content Overflow */
html, body {
    overflow-x: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}
/* Push footer to the bottom */
.main-content {
    flex: 1; /* Takes up all available space */
    padding-bottom: 20px; /* Space before footer */
}

/* ✅ Fix Navbar (Horizontal Layout) */
.navbar {
    height: 60px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: #d9b3ff; /* Light purple shade */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

/* ✅ Horizontal Alignment for Navbar Elements */
.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.navbar-center {
    display: flex;
    flex: 2;
    justify-content: center;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
    position: relative;
    width: 200px;
    padding-right: 30px;
}

/* ✅ Vertical Separator */
.navbar-separator {
    width: 1px;
    height: 25px;
    background-color: #4b0082; /* Light Purple */
    margin: 0 10px;
    opacity: 0.6;
}

/* ✅ Sidebar Toggle Button (☰) */
.sidebar-toggle {
    font-size: 24px;
    background: none;
    border: none;
    color: #4b0082;
    cursor: pointer;
}

/* ✅ Fix Logo */
.navbar-brand img {
    height: 40px;
}

/* ✅ Fix Search Bar */
.search-input {
    width: 300px;
    border-radius: 20px;
    padding: 5px 10px;
}
.search-btn {
    margin-left: 5px;
    border-radius: 20px;
}

/* ✅ Common Dropdown Styles */
.language-menu, .profile-menu {
    position: relative;
    display: inline-block;
}

.language-btn, .profile-btn {
    border-radius: 50%;
    padding: 8px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    color: #4b0082; /* Dark Purple (Same as Sidebar Toggle) */
    transition: background 0.3s ease;
}

.language-btn:hover, .profile-btn:hover {
    background: rgba(75, 0, 130, 0.1); /* Light Purple Hover Effect */
}

/* ✅ Dropdown Menu Styling */
.dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    background: #fff; /* White Background */
    border: 1px solid #d9b3ff; /* Light Purple Border */
    padding: 10px;
    width: 200px;
    flex-direction: column;
    text-align: left;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 100;
    border-radius: 5px;
}

/* ✅ Language Search Box */
.language-search {
    width: 100%;
    padding: 8px;
    border: 1px solid #d9b3ff; /* Light Purple Border */
    border-radius: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    outline: none;
    color: #4b0082; /* Dark Purple Text */
}

/* ✅ Scrollable Language List */
.language-list {
    max-height: 150px; /* Limits the height and makes it scrollable */
    overflow-y: auto;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #d9b3ff #f4f4f4; /* Custom Scrollbar */
}

/* ✅ Custom Scrollbar for Webkit Browsers */
.language-list::-webkit-scrollbar {
    width: 8px;
}

.language-list::-webkit-scrollbar-thumb {
    background: #d9b3ff; /* Light Purple */
    border-radius: 5px;
}

.language-list::-webkit-scrollbar-track {
    background: #f4f4f4;
}

/* ✅ Dropdown Menu Items */
.dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #d9b3ff; /* Light Purple Divider */
    text-decoration: none;
    color: #4b0082; /* Dark Purple Text */
    transition: background 0.2s ease-in-out;
}

/* ✅ Font Awesome Icons Styling */
.dropdown-menu a i {
    margin-right: 10px;
}

/* ✅ Remove Border for Last Item */
.dropdown-menu a:last-child {
    border-bottom: none;
}

/* ✅ Hover Effect */
.dropdown-menu a:hover {
    background: #d9b3ff; /* Light Purple Hover Background */
    color: #fff; /* White Text */
}

/* ✅ Display Dropdown When Active */
.language-menu.active .dropdown-menu,
.profile-menu.active .dropdown-menu {
    display: block;
}



/* ✅ Fix Sidebar */
.sidebar {
    width: 250px;
    height: 100vh;
    position: fixed;
    top: 60px;
    left: 0;
    background: #6a0dad;
    color: white;
    padding-top: 20px;
    transition: all 0.3s;
    z-index: 999;
}
.sidebar.collapsed {
    width: 80px;
}
.sidebar ul {
    padding-left: 0;
    list-style: none;
}
.sidebar ul li {
    padding: 15px;
    display: flex;
    align-items: center;
}
.sidebar ul li a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}
.sidebar.collapsed ul li a span {
    display: none;
}
.sidebar ul li:hover {
    background: #5a0bab;
}

/* ✅ Fix Dashboard Content (Avoid Overlapping with Sidebar) */
/* ✅ Override Bootstrap's Max-Width */
/* ✅ Ensure Full-Width Without Overflow */
.container {
  /*   max-width: calc(100% - 250px) !important; Adjust width based on sidebar */
    width: 100%;
    transition: margin-left 0.1s ease-in-out;
    overflow-x: hidden; /* Prevents horizontal scroll */
}

/* ✅ When Sidebar is Collapsed */
.sidebar.collapsed + .container {
    max-width: calc(100% - 80px) !important; /* Adjust width based on collapsed sidebar */
    margin-left: 80px !important;
}

/* ✅ When Sidebar is Expanded */
.sidebar + .container {
    margin-left: 250px !important;
    max-width: calc(100% - 250px) !important;
}



/* ✅ Manage Portal Tabs Styling */
.nav-tabs {
    border-bottom: 2px solid #6a0dad;
    display: flex;
    justify-content: center;
    background: white;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}
.nav-tabs .nav-item {
    margin-right: 10px;
    list-style: none;
}
.nav-tabs .nav-item .nav-link {
    color: #6a0dad;
    font-weight: bold;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
}
.nav-tabs .nav-item .nav-link.active {
    background-color: #6a0dad;
    color: white;
    border-radius: 5px;
}


.nav-tabs .nav-link.active {
    background-color: #6a0dad !important;  /* Dark purple */
    color: white !important;  /* White text */
    border-color: #ddd;
}

/* ✅ Also ensure hover effect */
.nav-tabs .nav-link:hover {
    background-color: #8c52ff !important;
    color: white !important;
}

/* ✅ Tab Error Styling for Validation */
.nav-tabs .nav-link.tab-error {
    border: 2px solid #dc3545 !important;
    color: #dc3545 !important;
    background-color: #fff5f5 !important;
    position: relative;
}

.nav-tabs .nav-link.tab-error::after {
    content: "!";
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.nav-tabs .nav-link.tab-error:hover {
    background-color: #f8d7da !important;
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

/* ✅ Tab Content */
.tab-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

/* ✅ User Management & User Settings Side by Side */
.user-section {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 20px;
}
.user-box {
    padding: 20px;
    cursor: pointer;
    border: 1px solid #6a0dad;
    border-radius: 8px;
    background-color: white;
    transition: 0.3s;
}
.user-box:hover {
    background: #f4e1ff; /* Light Purple Shade */
    box-shadow: 0px 4px 8px rgba(106, 13, 173, 0.2);
}
.user-box h5 {
    color: #6a0dad;
}
.user-box p {
    font-size: 14px;
    color: #555;
}

.site-footer .site-info {
    text-align: center;
    padding: 10px;
    background: #6a0dad;
    color: white;
    position: fixed;
    bottom: 0;
    width: 100%;
}

/* ✅ Login Page Styling */

/* Reset all margins and padding for login page */
.login-body * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.login-body html,
.login-body body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.login-body {
    margin: 0;
    padding: 0;
    height: 100vh;
    background: linear-gradient(to right, #6a0dad, #b39ddb);
}

.login-container {
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to right, #6a0dad, #b39ddb);
}

.login-box {
    display: flex;
    width: 600px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.login-left {
    width: 40%;
    background: #6a0dad;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.login-left img {
    width: 200px;
    height: auto;
}

.login-right {
    width: 60%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-title {
    font-size: 24px;
    font-weight: bold;
    color: #6a0dad;
    margin-bottom: 20px;
    text-align: center;
}

.login-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.login-button {
    background: #6a0dad;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    width: 100%;
    font-size: 16px;
    font-weight: bold;
}

.login-button:hover {
    background: #5a0bab;
}

.forgot-password {
    text-align: center;
    margin-top: 10px;
}

.forgot-password a {
    color: #6a0dad;
    text-decoration: none;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
}

/* ✅ Login Form Validation Styling */
.form-group {
    margin-bottom: 15px;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.error-message.show {
    display: block;
}

.error-message:empty {
    display: none;
}

.login-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* ✅ SSO Section Styling */
.sso-section {
    margin-top: 20px;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
}

.sso-providers {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.sso-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.sso-btn:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.sso-btn i {
    margin-right: 8px;
}

.spinner {
    margin-left: 10px;
}

/* ✅ Client Management Specific Styling */
.client-code-input {
    text-transform: uppercase;
}

.current-logo-preview {
    margin-top: 10px;
}

.current-logo-img {
    max-width: 100px;
    max-height: 50px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.progress-bar-thin {
    height: 4px;
}

.search-results-info {
    display: none;
}

.loading-indicator {
    display: none;
}

/* Client card styling improvements */
.client-card {
    transition: transform 0.2s ease-in-out;
}

.client-card:hover {
    transform: translateY(-2px);
}

.info-item {
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

/* Form validation styling for client forms */
.client-form .error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.client-form .error-message.show {
    display: block;
}

.client-form .error-message:empty {
    display: none;
}

.client-form .is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Loading states for buttons */
.btn-loading {
    position: relative;
}

.btn-loading .spinner {
    margin-right: 0.5rem;
}

/* ✅ User Management Page Styling */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.filter-section .form-select,
.filter-section .form-control {
    min-width: 150px;
}

.filter-section .row {
    align-items: center;
}

.filter-section .col-md-auto {
    display: flex;
    align-items: center;
}

/* ✅ User Management Compact Filter Layout */
.user-management .filter-section {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px 15px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

/* Compact Filter Controls */
.compact-filter {
    width: auto;
    min-width: 110px;
    max-width: 140px;
    font-size: 0.875rem;
}

.compact-search {
    width: 200px;
}

.compact-search .form-control {
    font-size: 0.875rem;
}



/* Responsive adjustments */
@media (max-width: 1200px) {
    .user-management .filter-section .row {
        flex-wrap: wrap;
    }

    .user-management .filter-section .col-auto {
        margin-bottom: 8px;
    }
}

@media (max-width: 768px) {
    .user-management .filter-section {
        padding: 10px;
    }

    .compact-filter {
        min-width: 100px;
        max-width: 120px;
    }

    .compact-search {
        width: 180px;
    }

    .user-management .filter-section .ms-auto {
        margin-left: 0 !important;
        margin-top: 10px;
    }
}

.question-grid th {
    background-color: #6a0dad;
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.question-grid td {
    vertical-align: middle;
}

.btn-clear-filters {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-clear-filters:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.search-results-info {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 15px;
}

#loadingIndicator {
    padding: 40px 0;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.fade-transition {
    transition: opacity 0.3s ease-in-out;
}

.filter-section .form-select:focus,
.filter-section .form-control:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

.page-link {
    color: #6a0dad;
}

.page-link:hover {
    color: #5a0b8a;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.no-results-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-results-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #dee2e6;
}

.user-grid {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
}

.user-grid th, .user-grid td {
    padding: 10px;
    border: 1px solid #ddd;
}

.user-grid th {
    background: #6a0dad;
    color: white;
}

/* Action buttons styling removed - now using standard Bootstrap btn classes to match assessment grid */

/* ✅ Add User Page Styling */
.add-user-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
   /* margin-top: 20px; */
}

.add-user-tabs {
    display: flex;
    border-bottom: 2px solid #6a0dad;
    margin-bottom: 20px;
}

.add-user-tabs .tab {
    padding: 10px 20px;
    cursor: pointer;
    font-weight: bold;
    color: #6a0dad;
    border-bottom: 2px solid transparent;
}

.add-user-tabs .tab.active {
    border-bottom: 2px solid #6a0dad;
}

.tabcontent- {
    display: block;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.tab-content.active {
    display: block;
}

.input-field {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-bottom: 15px;
}

.submit-btn {
    background: #6a0dad;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: bold;
}

.submit-btn:hover {
    background: #5a0bab;
}

/* Container for search input and icon */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-bar {
    width: 100%;
    padding: 10px 40px 10px 10px; /* Adjust right padding to accommodate the icon */
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
}

/* ✅ Search Bar Focus Styling */
.search-bar:focus,
.search-input:focus,
.language-search:focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
    outline: none !important;
}

.search-icon {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    color: #6a0dad; /* Match your theme color */
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-icon:hover {
    color: #4b0082; /* Darker shade for hover effect */
}


/* ✅ Tabs Content */
.tab-pane {
    display: none; /* Hide all tabs initially */
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* ✅ Only the active tab should be visible */
.tab-pane.show.active {
    display: block;
}

/* ✅ Align Submit and Cancel Buttons to the Right */

.form-group {
    margin-bottom: 15px; /* Add consistent spacing between form fields */
}

/* ✅ Ensure Two Fields Appear Side by Side */
.form-group-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Ensure spacing between fields */
}

.form-group-row .form-group {
    flex: 1;
    min-width: 45%;
}

/* ✅ Center Submit & Cancel Buttons */
.form-actions {
    display: flex;
    justify-content: center; /* Center align buttons */
    gap: 15px; /* Space between buttons */
    /*margin-top: 25px; Ensure spacing from form fields */
}

/* ✅ Theme Consistency for Buttons */
.btn-primary {
    background-color: #6a0dad !important; /* Theme primary button */
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    color: white;
    transition: 0.3s;
}

.btn-primary:hover {
    background-color: #4b0082 !important; /* Darker shade on hover */
}

.btn-danger {
    background-color: #dc3545 !important; /* Red cancel button */
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    color: white;
    transition: 0.3s;
}

.btn-danger:hover {
    background-color: #a71d2a !important; /* Darker red on hover */
}

/* ✅ Sidebar Styling */
#sidebar {
    background-color: #6a0dad !important; /* Theme primary color */
    min-height: 100vh;
    padding-top: 20px;
}

/* ✅ List Group Theme Fix */
.list-group {
    background-color: #6a0dad !important; /* Sidebar background */
    border-radius: 8px;
}

/* ✅ List Group Items */
.list-group-item {
    background-color: transparent !important;
    color: white !important;
    border: none !important;
    padding: 12px 15px;
    font-size: 16px;
    transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
}

/* ✅ Hover & Active State */
.list-group-item:hover, .list-group-item.active {
    background-color: #4b0082 !important; /* Darker shade for hover */
    color: #fff !important;
}

/* ✅ List Group Icons */
.list-group-item i {
    margin-right: 10px;
    font-size: 18px;
}
.input-error {
    border: 2px solid red !important;
    background-color: #ffe6e6; /* Light red background */
}

/* ✅ Theme Colors */
.theme-btn-primary {
    background-color: #6A0DAD; /* Theme Purple */
    color: white;
    border: none;
    padding: 6px 9px;
    border-radius: 5px;
    transition: background 0.3s ease-in-out;
}

.theme-btn-primary:hover {
    background-color: #530b8a; /* Darker shade */
    color: white !important; /* Maintain white text on hover */
}

.theme-btn-warning {
    background-color: #FFC107; /* Theme Yellow */
    color: black;
    border: none;
    padding: 6px 9px;
    border-radius: 5px;
    transition: background 0.3s ease-in-out;
}

.theme-btn-warning:hover {
    background-color: #e0a800; /* Darker shade */
}

.theme-btn-danger {
    background-color: #DC3545; /* Theme Red */
    color: white;
    border: none;
    padding: 6px 9px;
    border-radius: 5px;
    transition: background 0.3s ease-in-out;
}

.theme-btn-danger:hover {
    background-color: #bd2130; /* Darker shade */
    color: white !important; /* Maintain white text on hover */
}

/* ✅ Consistent button sizing across all grids - let theme buttons use their natural size */


/* ✅ Uniform Button Sizing */
.btn-sm {
    min-width: 35px;
    text-align: center;
}

/* ✅ Page Title */
.page-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: #6a0dad; /* Theme Purple */
}

/* ✅ Custom Tabs */
.custom-tabs .nav-link {
    color: #4b0082; /* Indigo */
    font-weight: bold;
    border: none;
}

.custom-tabs .nav-link.active {
    background-color: #6a0dad; /* Purple */
    color: white;
    border-radius: 5px;
}

/* ✅ Card Styles */
.card {
    background: #ffffff;
    border-radius: 8px;
    transition: all 0.3s ease-in-out;
    border-radius: 12px;
    box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.1);
}

.card h5 {
    color: #6a0dad; /* Purple */
}

.card:hover {
    transform: scale(1.03);
    box-shadow: 0px 4px 8px rgba(106, 13, 173, 0.2);
}

.card-body i {
    color: #6c757d;
}

/* ✅ Tag Container Styles for Modal Forms */
.tag-container {
    min-height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    background-color: #fff;
}

.tag-input-container {
    display: flex;
    flex-wrap: wrap;
    min-height: 40px;
    align-items: center;
    padding: 5px;
}

.tag {
    background-color: #6c5ce7;
    color: #fff;
    border-radius: 20px;
    padding: 5px 10px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
}

.tag .remove-tag {
    background: none;
    border: none;
    color: #fff;
    margin-left: 5px;
    cursor: pointer;
}

.tag-input-container input {
    border: none;
    outline: none;
    flex-grow: 1;
}

/* ✅ Tag Input Container Focus Styling */
.tag-input-container:focus-within {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

.tag-input-container input:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}


/* ✅ SCORM Card Styling */
.scorm-card {
    background: #fff;
    border: 2px solid #6a0dad; /* Theme color */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.scorm-card:hover {
    transform: scale(1.05);
}

.scorm-icon {
    font-size: 40px;
    color: #6a0dad; /* Theme color */
    margin-bottom: 10px;
}

.scorm-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.scorm-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.scorm-actions .edit-icon {
    color: #007bff; /* Blue */
    margin-right: 10px;
    cursor: pointer;
}

.scorm-actions .delete-icon {
    color: #dc3545; /* Red */
    cursor: pointer;
}

.scorm-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.scorm-actions i:hover {
    opacity: 0.7;
}
.scorm-actions a {
    text-decoration: none; /* Removes the underline */
}

.scorm-actions a:hover {
    text-decoration: none; /* Ensures no underline on hover */
}

/* ✅ SCORM File Preview Styling - Consistent with Other Modules */
#scormZipPreview .preview-wrapper {
    position: relative;
    display: inline-block;
    margin-right: 10px;
}

#scormZipPreview .remove-preview {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

#scormZipPreview .remove-preview:hover {
    background: #c82333;
}

#scormZipPreview p {
    margin: 0;
    word-break: break-all;
}

#scormZipPreview a {
    color: #6a0dad;
    text-decoration: none;
}

#scormZipPreview a:hover {
    text-decoration: underline;
}

/* External content CSS for card */
/* ✅ General Styling */
/* ✅ External Content Card Styling */
.content-card {
    background: #fff;
    border: 2px solid #6a0dad; /* Theme color - Purple */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.content-card:hover {
    transform: scale(1.05);
}

/* ✅ Dynamic Icons Based on Content Type */
.content-icon {
    font-size: 40px;
    margin-bottom: 10px;
}

/* ✅ Specific Icons for Different Sub-tabs */
.content-icon.video { color: #e84118; }       /* Red for YouTube/Vimeo */
.content-icon.course { color: #0984e3; }      /* Blue for LinkedIn/Udemy */
.content-icon.article { color: #2d3436; }     /* Dark Gray for Web Articles/Blogs */
.content-icon.podcast { color: #e67e22; }     /* Orange for Podcasts/Audio */

/* ✅ Title Styling */
.content-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ✅ Action Buttons (Edit & Delete) */
.content-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.content-actions .preview-icon {
    color: #28a745; /* Green */
    margin-right: 10px;
    cursor: pointer;
}

.content-actions .edit-icon {
    color: #00a8ff; /* Light Blue */
    margin-right: 10px;
    cursor: pointer;
}

.content-actions .delete-icon {
    color: #e74c3c; /* Red */
    cursor: pointer;
}

.content-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.content-actions i:hover {
    opacity: 0.7;
}

/* ✅ Ensuring No Underlines on Links */
.content-actions a {
    text-decoration: none;
}

.content-actions a:hover {
    text-decoration: none;
}

/* ✅ Assessment & Survey validation error styling */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
    display: block;
}

.invalid-feedback i {
    margin-right: 0.25rem;
}

/* Focus state for invalid fields */
.is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* ✅ File validation alerts in media preview */
#mediaPreview .alert {
    margin-top: 10px;
    padding: 8px 12px;
    font-size: 0.875em;
    border-radius: 4px;
}

#mediaPreview .alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

#mediaPreview .alert i {
    margin-right: 0.5rem;
}
/* Back arrow button code */
/* Container for back arrow and page title */
.back-arrow-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

/* Back link with arrow icon */
.back-link {
    text-decoration: none;
    font-size: 20px;
    color: #6a0dad; /* Theme Purple */
}

.back-link:hover {
    color: #4b0082; /* Accent color */
}

/* Vertical divider between icon and title */
.divider-line {
    border-left: 1px solid #ccc;
    height: 24px;
}

/* ✅ Assessment Card Styling (Same as SCORM) */
.assessment-card {
    background: #fff;
    border: 2px solid #6a0dad;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.assessment-card:hover {
    transform: scale(1.05);
}

.assessment-icon {
    font-size: 40px;
    color: #6a0dad;
    margin-bottom: 10px;
}

.assessment-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.assessment-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.assessment-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.assessment-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.assessment-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.assessment-actions i:hover {
    opacity: 0.7;
}

.assessment-actions a {
    text-decoration: none;
}

.assessment-actions a:hover {
    text-decoration: none;
}

.assessment-wrapper {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

.assessment-wrapper-border{
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* ✅ Audio Card Styling (Similar to Assessment but customizable) */
.audio-card {
    background: #fff;
    border: 2px solid #00796b; /* teal or customize */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.audio-card:hover {
    transform: scale(1.05);
}

.audio-icon {
    font-size: 40px;
    color: #00796b; /* matches border */
    margin-bottom: 10px;
}

.audio-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.audio-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.audio-actions .preview-icon {
    color: #28a745;
    margin-right: 10px;
    cursor: pointer;
}

.audio-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.audio-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.audio-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.audio-actions i:hover {
    opacity: 0.7;
}

.audio-actions a {
    text-decoration: none;
}

.audio-actions a:hover {
    text-decoration: none;
}

.audio-wrapper {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

.audio-wrapper-border {
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* ✅ Video Card Styling (Mirrors Audio Design with Distinct Theme) */
.video-card {
    background: #fff;
    border: 2px solid #3949ab; /* Indigo or customize */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.video-card:hover {
    transform: scale(1.05);
}

.video-icon {
    font-size: 40px;
    color: #3949ab; /* matches border */
    margin-bottom: 10px;
}

.video-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.video-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.video-actions .preview-icon {
    color: #28a745;
    margin-right: 10px;
    cursor: pointer;
}

.video-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.video-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.video-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.video-actions i:hover {
    opacity: 0.7;
}

.video-actions a {
    text-decoration: none;
}

.video-actions a:hover {
    text-decoration: none;
}

.video-wrapper {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

.video-wrapper-border {
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* ✅ Image Card Styling (Mirrors Video Design with Distinct Theme) */
.image-card {
    background: #fff;
    border: 2px solid #3949ab; /* Green or customize */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.image-card:hover {
    transform: scale(1.05);
}

.image-icon {
    font-size: 40px;
    color: #3949ab; /* matches border */
    margin-bottom: 10px;
}

.image-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.image-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.image-actions .preview-icon {
    color: #28a745;
    margin-right: 10px;
    cursor: pointer;
}

.image-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.image-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.image-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.image-actions i:hover {
    opacity: 0.7;
}

.image-actions a {
    text-decoration: none;
}

.image-actions a:hover {
    text-decoration: none;
}

.image-wrapper {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

.image-wrapper-border {
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

.image-thumbnail {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
}

/* ✅ Survey Card Styling (Mirrors Assessment Design with Distinct Theme) */
.survey-card {
    background: #fff;
    border: 2px solid #6a0dad; ; /* Purple theme for surveys */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.survey-card:hover {
    transform: scale(1.05);
}

.survey-icon {
    font-size: 40px;
    color: #6a0dad;  /* matches border */
    margin-bottom: 10px;
}

.survey-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.survey-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.survey-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.survey-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.survey-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.survey-actions i:hover {
    opacity: 0.7;
}

.survey-actions a {
    text-decoration: none;
}

.survey-actions a:hover {
    text-decoration: none;
}

.survey-wrapper {
    margin-top: 20px;
}

.survey-wrapper-border {
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* ✅ Feedback Card Styling (Follows Consistent Theme Color) */
.feedback-card {
    background: #fff;
    border: 2px solid #6a0dad; /* Purple theme - consistent with other cards */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.feedback-card:hover {
    transform: scale(1.05);
}

.feedback-icon {
    font-size: 40px;
    color: #6a0dad; /* matches border and theme */
    margin-bottom: 10px;
}

.feedback-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.feedback-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.feedback-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.feedback-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.feedback-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.feedback-actions i:hover {
    opacity: 0.7;
}

.feedback-actions a {
    text-decoration: none;
}

.feedback-actions a:hover {
    text-decoration: none;
}

.feedback-wrapper {
    margin-top: 20px;
}

.feedback-wrapper-border {
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
}

/* Upload preview for question - large */
/* ✅ Modal Scroll Support */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* ✅ Upload Preview (Question Image - Larger) */
.question-upload-preview {
    width: 100%;
    max-height: 180px;
    object-fit: contain;
    margin-top: 10px;
    position: relative;
}

/* ✅ Upload Preview (Option Image - Slightly Bigger Now) */
.option-upload-preview {
    width: 120px;
    height: 100px;
    object-fit: contain;
    margin-top: 5px;
    position: relative;
}

/* ✅ Cross Button - Square Shape */
.cross-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #fff;
    border: 1px solid #999;
    color: #000;
    width: 22px;
    height: 22px;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 2px; /* Square */
    z-index: 2;
}
.cross-icon:hover {
    background: #f8d7da;
    color: #721c24;
}

/* Ensure the preview container is position: relative */
.preview-container {
    position: relative;
    display: inline-block;
}
.preview-wrapper {
    position: relative;
    display: inline-block;
    margin-top: 10px;
}

.preview-wrapper img,
.preview-wrapper video,
.preview-wrapper iframe {
    max-width: 100%;
    border: 1px solid #ccc;
    border-radius: 5px;
}

/* Preview Sizes */
.preview-wrapper.question-preview img,
.preview-wrapper.question-preview video,
.preview-wrapper.question-preview iframe {
    width: 100%;
    max-height: 250px;
}

.preview-wrapper.option-preview img,
.preview-wrapper.option-preview video,
.preview-wrapper.option-preview iframe {
    width: 160px;
    max-height: 120px;
}

/* Cross Button */
.remove-preview {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border: none;
    width: 22px;
    height: 22px;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    text-align: center;
    border-radius: 3px; /* Square */
    cursor: pointer;
}

/* ✅ Spacing below pagination to prevent overlap with footer */
.pagination-container {
    margin-top: 30px;
    margin-bottom: 40px; /* Space above footer */
}

/* ✅ Pagination Styles */
.pagination .page-item .page-link {
    color: #4b0082; /* Indigo */
    background-color: #ffffff;
    border: 1px solid #ddd;
    font-weight: 500;
    padding: 6px 12px;
    margin: 0 3px;
    border-radius: 4px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

/* Hover effect */
.pagination .page-item .page-link:hover {
    background-color: #e9d6fa; /* Light lavender */
    color: #4b0082;
    border-color: #6a0dad;
}

/* Active state */
.pagination .page-item.active .page-link {
    background-color: #6a0dad; /* Theme Purple */
    color: white;
    border-color: #6a0dad;
    font-weight: 600;
    position: static;
}

/* Disabled state */
.pagination .page-item.disabled .page-link {
    color: #ccc;
    background-color: #f8f9fa;
    border-color: #ddd;
}

/* ✅ Interactive & AI Powered Content Package Cards */
.interactive-card {
    border: 2px solid #6a0dad;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    background-color: #f9f9f9;
    transition: transform 0.2s ease-in-out;
    position: relative; /* Required for absolute positioning of actions */
}

.interactive-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.interactive-icon {
    font-size: 40px;
    color: #6a0dad;
    margin-bottom: 10px;
}

.interactive-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}



/* Interactive Content Type Specific Fields */
.ai-tutoring-fields,
.ar-vr-fields,
.adaptive-learning-fields {
    display: none;
}

.ai-tutoring-fields.show,
.ar-vr-fields.show,
.adaptive-learning-fields.show {
    display: block;
}

/* Interactive Content Form Styling */
#interactiveModal .modal-dialog {
    max-width: 90%;
}

#interactiveModal .form-group {
    margin-bottom: 1rem;
}

#interactiveModal .form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
}

#interactiveModal .form-control:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

/* Interactive Content Tag Styling - Match SCORM Styling */
#interactiveTagDisplay .tag {
    background-color: #6c5ce7;
    color: #fff;
    border-radius: 20px;
    padding: 5px 10px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
}

#interactiveTagDisplay .tag .remove-tag {
    background: none;
    border: none;
    color: #fff;
    margin-left: 5px;
    cursor: pointer;
}

#interactiveTagDisplay .remove-tag:hover {
    color: #ff6b6b;
}

/* Interactive Content File Upload Styling */
.interactive-file-upload {
    border: 2px dashed #6a0dad;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin: 10px 0;
    background-color: #f8f9fa;
}

.interactive-file-upload:hover {
    background-color: #e9ecef;
}

/* Interactive Content Conditional Fields Animation */
.ai-tutoring-fields,
.ar-vr-fields,
.adaptive-learning-fields {
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

/* Interactive Content Icons for Different Types */
.interactive-card[data-type="adaptive_learning"] .interactive-icon {
    color: #28a745; /* Green for adaptive learning */
}

.interactive-card[data-type="ai_tutoring"] .interactive-icon {
    color: #007bff; /* Blue for AI tutoring */
}

.interactive-card[data-type="ar_vr"] .interactive-icon {
    color: #dc3545; /* Red for AR/VR */
}

/* Interactive Content Actions - Top Right Corner */
.interactive-actions {
    position: absolute;
    top: 10px;
    right: 15px;
}

.interactive-actions .edit-icon {
    color: #007bff;
    margin-right: 10px;
    cursor: pointer;
}

.interactive-actions .delete-icon {
    color: #dc3545;
    cursor: pointer;
}

.interactive-actions i {
    font-size: 18px;
    transition: color 0.2s;
}

.interactive-actions i:hover {
    opacity: 0.7;
}

.interactive-actions a {
    text-decoration: none;
}

.interactive-actions a:hover {
    text-decoration: none;
}

/* Interactive Content File Preview Styling */
#interactiveModal .preview-wrapper {
    position: relative;
    display: inline-block;
    margin-top: 10px;
}

#interactiveModal .preview-wrapper img {
    border: 1px solid #ddd;
    border-radius: 5px;
}

#interactiveModal .remove-preview {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border: none;
    width: 22px;
    height: 22px;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    text-align: center;
    border-radius: 3px; /* Square like other modules */
    cursor: pointer;
    z-index: 10;
}

#interactiveModal .remove-preview:hover {
    background: #c82333;
}

/* ✅ Non-SCORM Package Styles (Following SCORM Pattern) */
.nonscorm-card {
    background: #fff;
    border: 2px solid #6a0dad; /* Theme color */
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.2s ease-in-out;
}

.nonscorm-card:hover {
    transform: scale(1.05);
}

.nonscorm-card .card-body {
    padding: 0;
    text-align: center;
}

.nonscorm-icon {
    font-size: 48px;
    color: #6a0dad;
    margin-bottom: 15px;
}

.nonscorm-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nonscorm-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
}

.nonscorm-actions a {
    text-decoration: none;
}

.nonscorm-actions .edit-icon {
    color: #007bff;
    font-size: 16px;
    transition: color 0.2s ease;
}

.nonscorm-actions .edit-icon:hover {
    color: #0056b3;
}

.nonscorm-actions .delete-icon {
    color: #dc3545;
    font-size: 16px;
    transition: color 0.2s ease;
}

.nonscorm-actions .delete-icon:hover {
    color: #c82333;
}

/* Non-SCORM Sub-tabs */
#nonScormSubTabs {
    border-bottom: 2px solid #6a0dad;
    margin-bottom: 20px;
}

#nonScormSubTabs .nav-link {
    color: #6a0dad;
    border: none;
    border-bottom: 2px solid transparent;
    font-weight: bold;
    padding: 10px 15px;
}

#nonScormSubTabs .nav-link:hover {
    color: #5a0b9a;
    border-bottom-color: #5a0b9a;
}

#nonScormSubTabs .nav-link.active {
    color: #6a0dad;
    border-bottom-color: #6a0dad;
    background-color: transparent;
}

/* Non-SCORM Modal Styles */
#nonScormModal .modal-dialog {
    max-width: 90%;
}

/* Content Type Specific Fields */
.html5-fields,
.flash-fields,
.unity-fields,
.custom-web-fields,
.mobile-app-fields {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    background-color: #f8f9fa;
}

.html5-fields h6,
.flash-fields h6,
.unity-fields h6,
.custom-web-fields h6,
.mobile-app-fields h6 {
    color: #6a0dad;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Non-SCORM File Preview Styles */
#nonScormModal .preview-wrapper {
    position: relative;
    display: inline-block;
    margin-top: 10px;
}

#nonScormModal .preview-wrapper img {
    border: 1px solid #ddd;
    border-radius: 5px;
}

#nonScormModal .remove-preview {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border: none;
    width: 22px;
    height: 22px;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    text-align: center;
    border-radius: 3px; /* Square like other modules */
    cursor: pointer;
    z-index: 10;
}

#nonScormModal .remove-preview:hover {
    background: #c82333;
}

#nonScormModal .remove-preview {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border: none;
    width: 22px;
    height: 22px;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    z-index: 10;
}

#nonScormModal .remove-preview:hover {
    background: #c82333;
}

/* Non-SCORM Tag Styles - Match SCORM Styling */
#nonScormModal .tag-input-container {
    min-height: 40px;
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

#nonScormModal .tag {
    background-color: #6c5ce7;
    color: #fff;
    border-radius: 20px;
    padding: 5px 10px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
}

#nonScormModal .tag .remove-tag {
    background: none;
    border: none;
    color: #fff;
    margin-left: 5px;
    cursor: pointer;
}

#nonScormModal #nonscormTagInput {
    border: none;
    outline: none;
    flex: 1;
    min-width: 100px;
    padding: 4px;
}

/* Non-SCORM Form Validation Styles */
#nonScormModal .is-invalid {
    border-color: #dc3545;
}

#nonScormModal .error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Non-SCORM Responsive Design */
@media (max-width: 768px) {
    .nonscorm-card {
        margin-bottom: 15px;
    }

    .nonscorm-icon {
        font-size: 36px;
    }

    .nonscorm-title {
        font-size: 14px;
        min-height: 35px;
    }

    #nonScormModal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }

    #nonScormSubTabs .nav-link {
        padding: 8px 12px;
        font-size: 14px;
        font-weight: bold;
    }
}

/* Interactive Content Error Styling - Match Other Modules */
#interactiveModal .invalid-feedback {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
    display: block;
}

#interactiveModal .invalid-feedback i {
    margin-right: 0.25rem;
}

#interactiveModal .is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

#interactiveModal .is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* ✅ Global Form Control Theme Styling - Consistent Across All Modules */
.form-control:focus,
.form-select:focus,
textarea.form-control:focus,
input.form-control:focus,
select.form-control:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus,
input[type="datetime-local"]:focus,
input[type="time"]:focus,
textarea:focus,
select:focus {
    border-color: #6a0dad !important;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ Bootstrap Custom Form Controls Theme Styling */
.form-check-input:focus,
.form-range:focus,
.btn-check:focus + .btn,
.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ Modal Form Controls Theme Styling */
.modal .form-control:focus,
.modal .form-select:focus,
.modal textarea:focus,
.modal input:focus,
.modal select:focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ Override Bootstrap Default Focus Styles */
.form-control:not(.is-invalid):focus,
.form-select:not(.is-invalid):focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ Ensure Theme Color for All Input Types */
input:not([type="checkbox"]):not([type="radio"]):not(.btn):focus,
textarea:not(.btn):focus,
select:not(.btn):focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ High Priority Theme Color Override - Ensures Purple Theme Across All Forms */
body .form-control:focus,
body .form-select:focus,
body input:focus,
body textarea:focus,
body select:focus,
.container .form-control:focus,
.container .form-select:focus,
.container input:focus,
.container textarea:focus,
.container select:focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
    outline: none !important;
}

/* ✅ Specific Bootstrap Form States Override */
.was-validated .form-control:valid:focus,
.was-validated .form-select:valid:focus,
.form-control.is-valid:focus,
.form-select.is-valid:focus {
    border-color: #6a0dad !important;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25) !important;
}

/* ✅ Import Modal Theme Styling */
.import-modal .modal-header {
    background-color: #6a0dad !important;
    color: white !important;
}

.import-modal .btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.import-modal .alert-theme {
    background-color: rgba(106, 13, 173, 0.1) !important;
    border-color: #6a0dad !important;
    color: #6a0dad !important;
}

.import-modal .card-theme {
    border-color: #6a0dad !important;
}

.import-modal .card-header-theme {
    background-color: #6a0dad !important;
    color: white !important;
}

.import-modal .btn-outline-theme {
    border-color: #6a0dad !important;
    color: #6a0dad !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.import-modal .btn-outline-theme:hover {
    background-color: #6a0dad !important;
    color: white !important;
}

.import-modal .btn-theme {
    background-color: #6a0dad !important;
    border-color: #6a0dad !important;
    color: white !important;
}

.import-modal .btn-theme:hover {
    background-color: #5a0b9d !important;
    border-color: #5a0b9d !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(106, 13, 173, 0.3);
}

.import-modal .btn-theme:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(106, 13, 173, 0.3);
}

/* ✅ Template Download Buttons Styling */
.import-modal .d-grid .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.import-modal .d-grid .btn i {
    width: 16px;
    text-align: center;
}

/* ✅ Universal File Upload Styling - Consistent Across All Modules */
.form-control[type="file"] {
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control[type="file"]:focus {
    border-color: #6a0dad;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

.form-control[type="file"]:hover {
    border-color: #6a0dad;
}

/* File Upload Format Hints */
.text-muted {
    font-size: 0.875rem;
    color: #6c757d !important;
    margin-top: 0.25rem;
    display: block;
}

/* File Preview Containers - Universal */
.mt-2 {
    margin-top: 0.5rem !important;
}

/* File Upload Labels */
.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #212529;
}

/* Required Field Asterisk */
.text-danger {
    color: #dc3545 !important;
}

/* File Upload Error States */
.form-control[type="file"].is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control[type="file"].is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* ✅ Toast Notification Styles */
.toast-container {
    z-index: 9999 !important;
    max-width: 400px;
}

.toast {
    min-width: 300px;
    max-width: 400px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

.toast.show {
    animation: slideInRight 0.3s ease-out;
}

.toast.hide {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-body {
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
}

.toast-body i {
    font-size: 16px;
    margin-right: 8px;
}

.toast .btn-close {
    padding: 8px;
    margin: 4px;
}

/* ✅ Theme-Consistent Toast Notifications */

/* Success Toast - Theme Green */
.toast.text-bg-success {
    background: linear-gradient(135deg, #198754, #20c997) !important;
    border-left: 4px solid #146c43;
    color: white !important;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

/* Error Toast - Theme Red */
.toast.text-bg-danger {
    background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
    border-left: 4px solid #b02a37;
    color: white !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Warning Toast - Theme Yellow */
.toast.text-bg-warning {
    background: linear-gradient(135deg, #ffc107, #ffca2c) !important;
    border-left: 4px solid #e0a800;
    color: #000 !important;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.toast.text-bg-warning .btn-close {
    filter: invert(1) grayscale(100%) brightness(0);
}

/* Info Toast - Theme Purple */
.toast.text-bg-info {
    background: linear-gradient(135deg, #6a0dad, #8c52ff) !important;
    border-left: 4px solid #530b8a;
    color: white !important;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* Primary Toast - Theme Purple */
.toast.text-bg-primary {
    background: linear-gradient(135deg, #6a0dad, #8c52ff) !important;
    border-left: 4px solid #530b8a;
    color: white !important;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* ✅ Theme-Consistent Confirmation Modal */
.theme-modal {
    border: 2px solid #6a0dad;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.2);
}

.theme-modal-header {
    background: linear-gradient(135deg, #6a0dad, #8c52ff);
    color: white;
    border-bottom: 2px solid #530b8a;
    border-radius: 8px 8px 0 0;
}

.theme-modal-header .modal-title {
    font-weight: 600;
    color: white;
}

.theme-modal-body {
    background: #f8f9fa;
    padding: 25px;
}

.theme-modal-message {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.theme-modal-subtext {
    color: #6c757d;
    font-style: italic;
}

.theme-modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 25px;
    border-radius: 0 0 8px 8px;
}

/* Theme Secondary Button */
.theme-btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.theme-btn-secondary:hover {
    background-color: #5a6268;
    color: white !important; /* Maintain white text on hover */
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Enhanced Theme Button Animations */
.theme-btn-primary:hover,
.theme-btn-danger:hover,
.theme-btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Ensure text colors don't change on hover for confirmation buttons */
.theme-btn-primary:hover {
    color: white !important;
}

.theme-btn-danger:hover {
    color: white !important;
}

.theme-btn-warning:hover {
    color: black !important;
}

/* ✅ Client Management Styles */
.client-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.client-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
    border-color: #6A0DAD;
}

.client-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.client-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 4px;
}

.client-logo-placeholder {
    width: 40px;
    height: 40px;
    background-color: #6A0DAD;
    border: 1px solid #6A0DAD;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.client-info .info-item {
    margin-bottom: 8px;
}

.client-info .info-item:last-child {
    margin-bottom: 0;
}

.text-purple {
    color: #6A0DAD !important;
}

.page-title {
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.feature-badges .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* ✅ Client Management Single Row Filter Layout */
.client-management .filter-section .row {
    flex-wrap: nowrap;
}

.client-management .filter-section .col-auto {
    flex: 0 0 auto;
    margin-right: 15px;
}

.client-management .filter-section .col-auto:last-child {
    margin-right: 0;
    margin-left: auto;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .client-management .filter-section .row {
        flex-wrap: wrap;
    }

    .client-management .filter-section .col-auto {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .client-management .filter-section .col-auto:last-child {
        margin-left: 0;
        width: 100%;
    }
}

/* Modal Animation Enhancement */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

.toast.text-bg-info .btn-close {
    filter: invert(1) grayscale(100%) brightness(0);
}

/* ✅ Toast Responsive Design */
@media (max-width: 768px) {
    .toast-container {
        left: 10px !important;
        right: 10px !important;
        top: 10px !important;
        max-width: none;
    }

    .toast {
        min-width: auto;
        max-width: none;
        width: 100%;
    }
}

/* ===================================
   USER MANAGEMENT MODAL STYLES
   =================================== */
.modal-xl {
    max-width: 90%;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal .nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.modal .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    color: #6a0dad;
    font-weight: 500;
}

.modal .nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #f8f9fa;
}

.modal .nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.modal .form-group {
    margin-bottom: 1rem;
}

.modal .form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.modal .form-control {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.modal .form-control:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

.modal .form-check-input:checked {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.modal .btn-primary {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.modal .btn-primary:hover {
    background-color: #5a0b8a;
    border-color: #5a0b8a;
}

.modal .text-danger {
    color: #dc3545 !important;
}

.modal .alert {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
}

.modal .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Modal form validation styles */
.modal .is-invalid {
    border-color: #dc3545;
}

.modal .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* Modal responsive adjustments */
@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 0.5rem;
    }

    .modal-body {
        max-height: 60vh;
        padding: 1rem;
    }

    .modal .row {
        margin: 0;
    }

    .modal .col-lg-6,
    .modal .col-md-6 {
        padding: 0 0.5rem;
    }
}



